#include "lvgl.h"
#include "../gui.h"
#include "../styles.h"
#include "scr_liv_tra.h"
#include "esp_log.h"

#ifdef __cplusplus
extern "C"
{
#endif
      extern void sendMessage(const char *message);
      extern void switch_to_screen(lv_obj_t *screen);
#ifdef __cplusplus
}
#endif

static const lv_font_t* POLICE_COUNTER = POLICE_BOLD_18;
static const lv_font_t* POLICE_TIMER = POLICE_14;

// Fonction de création de container de send
void create_send_container(int index, lv_obj_t *parent, const char *top_label, const char *bottom_label)
{
      lv_obj_t *container = lv_obj_create(parent);
      ui.top_band.sends[index].container = container;

      // Configuration du container
      lv_obj_set_size(container, lv_pct(25), lv_pct(100));
      lv_obj_align(container, LV_ALIGN_LEFT_MID, index * lv_pct(25), 0);
      lv_obj_add_style(container, &style_container, 0);
      lv_obj_add_flag(container, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_EVENT_BUBBLE);
      lv_obj_add_event_cb(container, send_container_event_cb, LV_EVENT_LONG_PRESSED, NULL);
      lv_obj_clear_flag(container, LV_OBJ_FLAG_SCROLLABLE);

      // Label du haut - aligné en haut à gauche
      lv_obj_t *label_top = lv_label_create(container);
      ui.top_band.sends[index].top_label = label_top;
      lv_label_set_text(label_top, top_label);
      lv_obj_set_size(label_top, lv_pct(85), LV_SIZE_CONTENT);
      lv_obj_align(label_top, LV_ALIGN_TOP_LEFT, 4, 4);
      lv_obj_set_style_text_font(label_top, POLICE_14, 0);
      lv_obj_set_style_text_align(label_top, LV_TEXT_ALIGN_LEFT, 0);
      lv_obj_add_style(label_top, &style_container_label, 0);
      lv_label_set_long_mode(label_top, LV_LABEL_LONG_CLIP);
      lv_obj_add_flag(label_top, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_EVENT_BUBBLE);

      // Barre - alignée sous le label du haut
      lv_obj_t *bar = lv_bar_create(container);
      ui.top_band.sends[index].bar = bar;
      lv_obj_set_size(bar, lv_pct(85), 25);
      lv_obj_align_to(bar, label_top, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 16);
      lv_bar_set_range(bar, 0, 100);
      lv_bar_set_value(bar, 0, LV_ANIM_OFF);
      lv_obj_add_style(bar, &style_bar_bg, LV_PART_MAIN | LV_STATE_DEFAULT);
      lv_obj_add_style(bar, &style_bar_indic, LV_PART_INDICATOR);
      lv_obj_add_style(bar, &style_shadow_little, LV_PART_MAIN);
      lv_obj_add_flag(bar, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_EVENT_BUBBLE);

      // Label du bas - aligné sous la barre
      lv_obj_t *label_bottom = lv_label_create(container);
      ui.top_band.sends[index].bottom_label = label_bottom;
      lv_label_set_text(label_bottom, bottom_label);
      lv_obj_set_size(label_bottom, lv_pct(85), LV_SIZE_CONTENT);
      lv_obj_align_to(label_bottom, bar, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 16);
      lv_obj_set_style_text_font(label_bottom, POLICE_14, 0);
      lv_obj_set_style_text_align(label_bottom, LV_TEXT_ALIGN_LEFT, 0);
      lv_obj_add_style(label_bottom, &style_container_label, 0);
      lv_label_set_long_mode(label_bottom, LV_LABEL_LONG_DOT);
      lv_obj_add_flag(label_bottom, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_EVENT_BUBBLE);

      lv_obj_add_flag(container, LV_OBJ_FLAG_HIDDEN);
}

// Fonction principale de création de l'écran
lv_obj_t *create_scr_liv_tra(void)
{
      ui.screen = lv_obj_create(NULL);
      lv_obj_set_style_bg_color(ui.screen, COULEUR_GRIS, 0);
      init_styles();

      create_left_band();

      create_bottom_band();

      create_top_band();

      create_relatives_container();
      
      create_info_container();

      
      // Create the relatives popup (initially hidden)
      create_relatives_popup();

      // Create the loop length popup
      create_loop_length_popup();

      // Create the cues popup
      create_cues_popup();

      // Create the loop settings popup
      create_loop_settings_popup();

      return ui.screen;
}

void create_left_band(void) {
      // Création de la bande gauche
      ui.left_band.band = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.left_band.band, lv_pct(LEFTBANDWIDTHINPERCENT), lv_pct(100));
      lv_obj_align(ui.left_band.band, LV_ALIGN_LEFT_MID, 0, 0);
      lv_obj_set_flex_flow(ui.left_band.band, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(ui.left_band.band, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
      lv_obj_add_style(ui.left_band.band, &style_leftband, 0);

      // Ajouter les 5 labels dans left_band
      const char *labels[] = {"fold.", "", "arm.", "mute.", "solo."};
      for (int i = 0; i < 5; i++)
      {
            lv_obj_t *container = lv_obj_create(ui.left_band.band);
            lv_obj_set_size(container, lv_pct(100), LV_SIZE_CONTENT);
            lv_obj_align(container, LV_ALIGN_LEFT_MID, 0, 0);
            lv_obj_set_style_bg_opa(container, LV_OPA_0, 0);
            lv_obj_set_style_border_width(container, 0, 0);
            lv_obj_set_style_pad_all(container, 0, 0);
            lv_obj_clear_flag(container, LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_add_flag(container, LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(container, common_leftband_label_handler, LV_EVENT_SHORT_CLICKED, NULL);

            lv_obj_t *label = lv_label_create(container);
            ui.left_band.labels[i] = label; // Stockage de la référence du label
            lv_label_set_text(label, labels[i]);
            lv_obj_align(label, LV_ALIGN_LEFT_MID, 5, 0);
            lv_obj_set_style_text_font(label, POLICE_18, 0);
            lv_obj_add_style(label, &style_mode_others, 0);
            lv_obj_add_flag(label, LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(label, common_leftband_label_handler, LV_EVENT_SHORT_CLICKED, NULL);

            lv_obj_center(container);
      }
}

void create_relatives_popup(void) {
      // Create the background container that covers the entire screen
      ui.relatives_popup.background_container = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.relatives_popup.background_container, lv_pct(100), lv_pct(100));
      lv_obj_align(ui.relatives_popup.background_container, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_style_bg_color(ui.relatives_popup.background_container, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_opa(ui.relatives_popup.background_container, LV_OPA_50, 0);
      lv_obj_set_style_border_width(ui.relatives_popup.background_container, 0, 0);
      lv_obj_add_flag(ui.relatives_popup.background_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.relatives_popup.background_container, relatives_popup_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      // Create the title label
      ui.relatives_popup.title_label = lv_label_create(ui.relatives_popup.background_container);
      lv_label_set_text(ui.relatives_popup.title_label, "daughters/sisters");
      lv_obj_set_style_text_font(ui.relatives_popup.title_label, POLICE_20, 0);
      lv_obj_set_style_text_color(ui.relatives_popup.title_label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.relatives_popup.title_label, LV_ALIGN_TOP_MID, 0, 20);

      // Create the container for the track buttons
      ui.relatives_popup.tracks_container = lv_obj_create(ui.relatives_popup.background_container);
      lv_obj_set_size(ui.relatives_popup.tracks_container, lv_pct(33), lv_pct(80));
      lv_obj_align(ui.relatives_popup.tracks_container, LV_ALIGN_CENTER, 0, 10);
      lv_obj_set_style_bg_color(ui.relatives_popup.tracks_container, COULEUR_GRIS_MOYEN, 0);
      lv_obj_set_style_radius(ui.relatives_popup.tracks_container, 10, 0);
      lv_obj_set_style_border_width(ui.relatives_popup.tracks_container, 0, 0);
      lv_obj_add_style(ui.relatives_popup.tracks_container, &style_shadow, 0);

      // Set up scrolling for the tracks container
      lv_obj_set_flex_flow(ui.relatives_popup.tracks_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_style_pad_all(ui.relatives_popup.tracks_container, 10, 0);
      lv_obj_set_style_pad_row(ui.relatives_popup.tracks_container, 15, 0);
      lv_obj_add_flag(ui.relatives_popup.tracks_container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_set_scroll_dir(ui.relatives_popup.tracks_container, LV_DIR_VER);
      lv_obj_set_scrollbar_mode(ui.relatives_popup.tracks_container, LV_SCROLLBAR_MODE_AUTO);
      lv_obj_set_scroll_snap_y(ui.relatives_popup.tracks_container, LV_SCROLL_SNAP_NONE);
      lv_obj_add_style(ui.relatives_popup.tracks_container, &style_scrollbar, LV_PART_SCROLLBAR);

      // Create the 64 track buttons
      for (int i = 0; i < 64; i++) {
            // Create button
            ui.relatives_popup.track_buttons[i] = lv_obj_create(ui.relatives_popup.tracks_container);
            lv_obj_set_size(ui.relatives_popup.track_buttons[i], lv_pct(100), 60); // About 12% of container height
            lv_obj_set_style_bg_color(ui.relatives_popup.track_buttons[i], COULEUR_GRIS, 0);
            lv_obj_set_style_radius(ui.relatives_popup.track_buttons[i], 5, 0);
            lv_obj_set_style_border_width(ui.relatives_popup.track_buttons[i], 2, 0);
            lv_obj_set_style_border_color(ui.relatives_popup.track_buttons[i], COULEUR_GRIS_FONCE, 0);
            lv_obj_add_style(ui.relatives_popup.track_buttons[i], &style_shadow_little, 0);
            lv_obj_clear_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_add_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(ui.relatives_popup.track_buttons[i], track_button_event_cb, LV_EVENT_CLICKED, (void*)(intptr_t)i);

            // Create label for the button
            ui.relatives_popup.track_labels[i] = lv_label_create(ui.relatives_popup.track_buttons[i]);
            lv_label_set_text_fmt(ui.relatives_popup.track_labels[i], "track %d", i + 1);
            lv_obj_set_style_text_font(ui.relatives_popup.track_labels[i], POLICE_14, 0);
            lv_obj_set_style_text_color(ui.relatives_popup.track_labels[i], COULEUR_BEIGE, 0);
            lv_obj_center(ui.relatives_popup.track_labels[i]);
      }

      // Initially hide the popup
      lv_obj_add_flag(ui.relatives_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}

void create_bottom_band(void) {

      // Création de la bande du bas (volume/pan/boutons)
      ui.bottom_band.band = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.bottom_band.band, lv_pct(100 - LEFTBANDWIDTHINPERCENT), lv_pct(60));
      lv_obj_align(ui.bottom_band.band, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.band, 0, 0);
      lv_obj_set_style_bg_opa(ui.bottom_band.band, LV_OPA_0, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.band, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.band, LV_OBJ_FLAG_SCROLLABLE);

      // Container volume (maintenant dans bottom band)
      ui.bottom_band.volume.container = lv_obj_create(ui.bottom_band.band);
      lv_obj_set_size(ui.bottom_band.volume.container, lv_pct(25), lv_pct(100));
      lv_obj_align(ui.bottom_band.volume.container, LV_ALIGN_LEFT_MID, 0, 0);
      lv_obj_set_style_bg_opa(ui.bottom_band.volume.container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.volume.container, 0, 0);
      lv_obj_add_flag(ui.bottom_band.volume.container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.volume.container, LV_OBJ_FLAG_EVENT_BUBBLE);
      lv_obj_add_event_cb(ui.bottom_band.volume.container, volume_container_event_cb,
                          LV_EVENT_LONG_PRESSED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.volume.container, volume_container_event_cb,
                          LV_EVENT_SHORT_CLICKED, NULL);

      // Barre de volume
      ui.bottom_band.volume.bar = lv_bar_create(ui.bottom_band.volume.container);
      lv_obj_set_size(ui.bottom_band.volume.bar, lv_pct(20), lv_pct(80));
      lv_obj_set_pos(ui.bottom_band.volume.bar, lv_pct(37.5), lv_pct(5));
      lv_bar_set_range(ui.bottom_band.volume.bar, 0, 100);
      lv_bar_set_value(ui.bottom_band.volume.bar, 85, LV_ANIM_OFF);
      lv_obj_add_style(ui.bottom_band.volume.bar, &style_bar_bg, LV_PART_MAIN);
      lv_obj_add_style(ui.bottom_band.volume.bar, &style_bar_indic, LV_PART_INDICATOR);
      lv_obj_set_style_bg_grad_dir(ui.bottom_band.volume.bar, LV_GRAD_DIR_VER, 0);
      lv_obj_add_flag(ui.bottom_band.volume.bar, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.volume.bar, LV_OBJ_FLAG_EVENT_BUBBLE);

      // Label volume
      ui.bottom_band.volume.label = lv_label_create(ui.bottom_band.volume.container);
      lv_obj_set_style_text_font(ui.bottom_band.volume.label, POLICE_20, 0);
      lv_obj_set_style_text_color(ui.bottom_band.volume.label, COULEUR_BEIGE, 0);
      lv_label_set_text(ui.bottom_band.volume.label, "0.0 dB");
      // lv_obj_align_to(ui.top_band.volume.label, ui.top_band.volume.bar, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
      lv_obj_align(ui.bottom_band.volume.label, LV_ALIGN_CENTER, 0, lv_pct(45));
      lv_obj_add_flag(ui.bottom_band.volume.label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.volume.label, LV_OBJ_FLAG_EVENT_BUBBLE);

      // Container pan (maintenant dans bottom band)
      ui.bottom_band.pan.container = lv_obj_create(ui.bottom_band.band);
      lv_obj_set_size(ui.bottom_band.pan.container, lv_pct(35), lv_pct(100));
      lv_obj_align_to(ui.bottom_band.pan.container, ui.bottom_band.volume.container, LV_ALIGN_OUT_RIGHT_MID, 0, 50);
      lv_obj_set_style_bg_opa(ui.bottom_band.pan.container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.pan.container, 0, 0);
    lv_obj_add_flag(ui.bottom_band.pan.container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.pan.container, LV_OBJ_FLAG_EVENT_BUBBLE);
      lv_obj_set_scrollbar_mode(ui.bottom_band.pan.container, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_event_cb(ui.bottom_band.pan.container, pan_container_event_cb,
                          LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.pan.container, pan_container_event_cb,
                          LV_EVENT_LONG_PRESSED, NULL);

      // Arc du pan
      ui.bottom_band.pan.arc = lv_arc_create(ui.bottom_band.pan.container);
      lv_obj_set_size(ui.bottom_band.pan.arc, lv_pct(100), lv_pct(70));
      lv_obj_align(ui.bottom_band.pan.arc, LV_ALIGN_CENTER, 0, lv_pct(15));
      lv_obj_clear_flag(ui.bottom_band.pan.arc, LV_OBJ_FLAG_CLICKABLE);

      // Masquer complètement le bouton de l'arc
      lv_obj_set_style_bg_opa(ui.bottom_band.pan.arc, 0, LV_PART_KNOB);
      lv_obj_set_style_border_opa(ui.bottom_band.pan.arc, 0, LV_PART_KNOB);
      lv_obj_set_style_pad_all(ui.bottom_band.pan.arc, 0, LV_PART_KNOB);
      lv_obj_set_style_size(ui.bottom_band.pan.arc, 0, LV_PART_KNOB);

      // Configuration de l'arc
      lv_arc_set_rotation(ui.bottom_band.pan.arc, 90);
      lv_arc_set_bg_angles(ui.bottom_band.pan.arc, 135, 225);
      lv_arc_set_mode(ui.bottom_band.pan.arc, LV_ARC_MODE_SYMMETRICAL);
      lv_arc_set_value(ui.bottom_band.pan.arc, 0);
      lv_arc_set_range(ui.bottom_band.pan.arc, -50, 50);
      // Styles de l'arc
      lv_obj_add_style(ui.bottom_band.pan.arc, &style_arc_pan_main, LV_PART_MAIN);
      lv_obj_add_style(ui.bottom_band.pan.arc, &style_arc_pan_indicator, LV_PART_INDICATOR);
      lv_obj_set_style_opa(ui.bottom_band.pan.arc, 0, LV_PART_KNOB);

      // Label du pan
      ui.bottom_band.pan.label = lv_label_create(ui.bottom_band.pan.container);
      lv_obj_set_style_text_font(ui.bottom_band.pan.label, POLICE_20, 0);
      lv_obj_set_style_text_color(ui.bottom_band.pan.label, COULEUR_BEIGE, 0);
      lv_label_set_text(ui.bottom_band.pan.label, "C");
      lv_obj_align(ui.bottom_band.pan.label, LV_ALIGN_CENTER, -16, lv_pct(20));
      lv_obj_add_flag(ui.bottom_band.pan.label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.pan.label, LV_OBJ_FLAG_EVENT_BUBBLE);

      // Container boutons (maintenant dans bottom band)
      ui.bottom_band.buttons.container = lv_obj_create(ui.bottom_band.band); // Notez que le parent change
      lv_obj_set_size(ui.bottom_band.buttons.container, lv_pct(30), lv_pct(100));
      lv_obj_align(ui.bottom_band.buttons.container, LV_ALIGN_TOP_RIGHT, 0, 0);
      lv_obj_set_style_bg_opa(ui.bottom_band.buttons.container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.container, 0, 0);

      // Création du bouton MUTE
      ui.bottom_band.buttons.mute.btn = lv_obj_create(ui.bottom_band.buttons.container);
      lv_obj_set_size(ui.bottom_band.buttons.mute.btn, lv_pct(40), lv_pct(20));
      lv_obj_align(ui.bottom_band.buttons.mute.btn, LV_ALIGN_TOP_LEFT, 0, 20);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.mute.btn, 8, 0);
      lv_obj_set_style_border_color(ui.bottom_band.buttons.mute.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.buttons.mute.btn, COULEUR_ORANGE, 0);
      lv_obj_set_style_radius(ui.bottom_band.buttons.mute.btn, 4, 0);
      lv_obj_set_scrollbar_mode(ui.bottom_band.buttons.mute.btn, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_style(ui.bottom_band.buttons.mute.btn, &style_shadow, 0);

      ui.bottom_band.buttons.mute.label = lv_label_create(ui.bottom_band.buttons.mute.btn);
      lv_label_set_text(ui.bottom_band.buttons.mute.label, " ");
      lv_obj_set_style_text_font(ui.bottom_band.buttons.mute.label, POLICE_28, 0);
      lv_obj_center(ui.bottom_band.buttons.mute.label);
      lv_obj_set_style_text_color(ui.bottom_band.buttons.mute.label, COULEUR_NOIRE, 0);

      lv_obj_add_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_CHECKABLE);
      lv_obj_add_event_cb(ui.bottom_band.buttons.mute.btn, mute_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.buttons.mute.btn, mute_button_event_cb, LV_EVENT_LONG_PRESSED, NULL);    


      // Création du bouton S (Solo)
      ui.bottom_band.buttons.solo.btn = lv_obj_create(ui.bottom_band.buttons.container);
      lv_obj_set_size(ui.bottom_band.buttons.solo.btn, lv_pct(40), lv_pct(20));
      lv_obj_align_to(ui.bottom_band.buttons.solo.btn, ui.bottom_band.buttons.mute.btn, LV_ALIGN_OUT_BOTTOM_MID, 0, 15);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.solo.btn, 8, 0);
      lv_obj_set_style_border_color(ui.bottom_band.buttons.solo.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.buttons.solo.btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.buttons.solo.btn, 4, 0);
      lv_obj_set_scrollbar_mode(ui.bottom_band.buttons.solo.btn, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_style(ui.bottom_band.buttons.solo.btn, &style_shadow, 0);

      ui.bottom_band.buttons.solo.label = lv_label_create(ui.bottom_band.buttons.solo.btn);
      lv_label_set_text(ui.bottom_band.buttons.solo.label, "S");
      lv_obj_set_style_text_font(ui.bottom_band.buttons.solo.label, POLICE_20, 0);
      lv_obj_center(ui.bottom_band.buttons.solo.label);
      lv_obj_set_style_text_color(ui.bottom_band.buttons.solo.label, COULEUR_BEIGE, 0);

      lv_obj_add_flag(ui.bottom_band.buttons.solo.btn, LV_OBJ_FLAG_CHECKABLE);
      lv_obj_add_event_cb(ui.bottom_band.buttons.solo.btn, solo_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.buttons.solo.btn, solo_button_event_cb, LV_EVENT_LONG_PRESSED, NULL);
      

      // Création du bouton Arm
      ui.bottom_band.buttons.arm_btn = lv_obj_create(ui.bottom_band.buttons.container);
      lv_obj_set_size(ui.bottom_band.buttons.arm_btn, lv_pct(40), lv_pct(20));
      lv_obj_align_to(ui.bottom_band.buttons.arm_btn, ui.bottom_band.buttons.mute.btn, LV_ALIGN_OUT_RIGHT_MID, 25, 0);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.arm_btn, 8, 0);
      lv_obj_set_style_border_color(ui.bottom_band.buttons.arm_btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.buttons.arm_btn, 4, 0);
      lv_obj_set_scrollbar_mode(ui.bottom_band.buttons.arm_btn, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_style(ui.bottom_band.buttons.arm_btn, &style_shadow, 0);
      lv_obj_add_flag(ui.bottom_band.buttons.arm_btn, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.buttons.arm_btn, arm_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);


      ui.bottom_band.buttons.arm_circle = lv_obj_create(ui.bottom_band.buttons.arm_btn);
      lv_obj_set_size(ui.bottom_band.buttons.arm_circle, 17, 17);
      lv_obj_set_style_radius(ui.bottom_band.buttons.arm_circle, LV_RADIUS_CIRCLE, 0);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.arm_circle, 0, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_circle, COULEUR_GRIS_CLAIR, 0);
      lv_obj_align(ui.bottom_band.buttons.arm_circle, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_scrollbar_mode(ui.bottom_band.buttons.arm_circle, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_flag(ui.bottom_band.buttons.arm_circle, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.buttons.arm_circle, arm_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

       // Création du bouton metronome
      ui.bottom_band.buttons.metro.btn = lv_obj_create(ui.bottom_band.buttons.container);
      lv_obj_set_size(ui.bottom_band.buttons.metro.btn, lv_pct(40), lv_pct(20));
      lv_obj_align_to(ui.bottom_band.buttons.metro.btn, ui.bottom_band.buttons.solo.btn, LV_ALIGN_OUT_RIGHT_MID, 25, 0);
      lv_obj_set_style_border_width(ui.bottom_band.buttons.metro.btn, 8, 0);
      lv_obj_set_style_border_color(ui.bottom_band.buttons.metro.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.buttons.metro.btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.buttons.metro.btn, 4, 0);
      lv_obj_set_scrollbar_mode(ui.bottom_band.buttons.metro.btn, LV_SCROLLBAR_MODE_OFF);
      lv_obj_add_style(ui.bottom_band.buttons.metro.btn, &style_shadow, 0);

      ui.bottom_band.buttons.metro.label = lv_label_create(ui.bottom_band.buttons.metro.btn);
      lv_label_set_text(ui.bottom_band.buttons.metro.label, "M");
      lv_obj_set_style_text_font(ui.bottom_band.buttons.metro.label, POLICE_20, 0);
      lv_obj_center(ui.bottom_band.buttons.metro.label);
      lv_obj_set_style_text_color(ui.bottom_band.buttons.metro.label, COULEUR_BEIGE, 0);

      lv_obj_add_flag(ui.bottom_band.buttons.metro.btn, LV_OBJ_FLAG_CHECKABLE);
      lv_obj_add_event_cb(ui.bottom_band.buttons.metro.btn, metro_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);  


     
      ui.bottom_band.song_state.container = lv_obj_create(ui.bottom_band.band);
      lv_obj_set_size(ui.bottom_band.song_state.container, lv_pct(16), lv_pct(100));
      lv_obj_align(ui.bottom_band.song_state.container, LV_ALIGN_TOP_RIGHT, -260, 0);
      lv_obj_set_style_bg_opa(ui.bottom_band.song_state.container, LV_OPA_0, 0);      
      lv_obj_set_style_border_width(ui.bottom_band.song_state.container, 0, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.song_state.container, 5, 0);

    
      ui.bottom_band.song_state.tempo_label = lv_label_create(ui.bottom_band.song_state.container);
      lv_label_set_text(ui.bottom_band.song_state.tempo_label, "120");
      lv_obj_set_style_text_font(ui.bottom_band.song_state.tempo_label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.bottom_band.song_state.tempo_label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.bottom_band.song_state.tempo_label, LV_ALIGN_BOTTOM_MID, 0, -DEFAUTPADDING);
      lv_obj_clear_flag(ui.bottom_band.song_state.tempo_label, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.tempo_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.song_state.tempo_label, tempo_label_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.bottom_band.song_state.loop.btn = lv_obj_create(ui.bottom_band.song_state.container);
      lv_obj_set_size(ui.bottom_band.song_state.loop.btn, lv_pct(100), lv_pct(20));
      lv_obj_align_to (ui.bottom_band.song_state.loop.btn, ui.bottom_band.song_state.tempo_label, LV_ALIGN_OUT_TOP_MID, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.song_state.loop.btn, 0, 0);      
      lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.song_state.loop.btn, 10, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.song_state.loop.btn, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.loop.btn, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_set_style_clip_corner(ui.bottom_band.song_state.loop.btn, true, LV_PART_MAIN);
      lv_obj_add_flag(ui.bottom_band.song_state.loop.btn, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.song_state.loop.btn, loop_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.song_state.loop.btn, loop_button_event_cb, LV_EVENT_LONG_PRESSED, NULL);
      
      ui.bottom_band.song_state.loop.punch_in = lv_obj_create(ui.bottom_band.song_state.loop.btn);
      lv_obj_set_size(ui.bottom_band.song_state.loop.punch_in, lv_pct(10), lv_pct(100));
      lv_obj_align(ui.bottom_band.song_state.loop.punch_in, LV_ALIGN_LEFT_MID, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.song_state.loop.punch_in, 0, 0);
      lv_obj_set_style_radius(ui.bottom_band.song_state.loop.punch_in, 0, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_in, COULEUR_GRIS, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.loop.punch_in, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop.punch_in, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop.punch_in, LV_OBJ_FLAG_EVENT_BUBBLE);
      
      ui.bottom_band.song_state.loop.punch_out = lv_obj_create(ui.bottom_band.song_state.loop.btn);
      lv_obj_set_size(ui.bottom_band.song_state.loop.punch_out, lv_pct(10), lv_pct(100));
      lv_obj_align(ui.bottom_band.song_state.loop.punch_out, LV_ALIGN_RIGHT_MID, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.song_state.loop.punch_out, 0, 0);
      lv_obj_set_style_radius(ui.bottom_band.song_state.loop.punch_out, 0, 0);
      lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_out, COULEUR_GRIS, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.loop.punch_out, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop.punch_out, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop.punch_out, LV_OBJ_FLAG_EVENT_BUBBLE);

      ui.bottom_band.song_state.loop_start.btn = lv_obj_create(ui.bottom_band.song_state.container);
      lv_obj_set_size(ui.bottom_band.song_state.loop_start.btn, lv_pct(100), lv_pct(20));
      lv_obj_align_to (ui.bottom_band.song_state.loop_start.btn, ui.bottom_band.song_state.loop.btn, LV_ALIGN_OUT_TOP_MID, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.song_state.loop_start.btn, 0, 0);      
      lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop_start.btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.song_state.loop_start.btn, 10, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.song_state.loop_start.btn, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.loop_start.btn, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop_start.btn, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.song_state.loop_start.btn, loop_start_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.song_state.loop_start.btn, loop_start_label_event_cb, LV_EVENT_LONG_PRESSED, NULL);

      ui.bottom_band.song_state.loop_start.label = lv_label_create(ui.bottom_band.song_state.loop_start.btn);
      lv_label_set_text(ui.bottom_band.song_state.loop_start.label, "Loop Start");
      lv_obj_set_style_text_font(ui.bottom_band.song_state.loop_start.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.bottom_band.song_state.loop_start.label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.bottom_band.song_state.loop_start.label, LV_ALIGN_CENTER, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.loop_start.label, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop_start.label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.loop_start.label, LV_OBJ_FLAG_EVENT_BUBBLE);
      

      ui.bottom_band.song_state.add_cue.btn = lv_obj_create(ui.bottom_band.song_state.container);
      lv_obj_set_size(ui.bottom_band.song_state.add_cue.btn, lv_pct(100), lv_pct(20));
      lv_obj_align_to (ui.bottom_band.song_state.add_cue.btn, ui.bottom_band.song_state.loop_start.btn, LV_ALIGN_OUT_TOP_MID, 0, 0);
      lv_obj_set_style_border_width(ui.bottom_band.song_state.add_cue.btn, 0, 0);      
      lv_obj_set_style_bg_color(ui.bottom_band.song_state.add_cue.btn, COULEUR_GRIS, 0);
      lv_obj_set_style_radius(ui.bottom_band.song_state.add_cue.btn, 10, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.song_state.add_cue.btn, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.add_cue.btn, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.add_cue.btn, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.bottom_band.song_state.add_cue.btn, add_cue_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);
      lv_obj_add_event_cb(ui.bottom_band.song_state.add_cue.btn, add_cue_button_event_cb, LV_EVENT_LONG_PRESSED, NULL);

      ui.bottom_band.song_state.add_cue.label = lv_label_create(ui.bottom_band.song_state.add_cue.btn);
      lv_label_set_text(ui.bottom_band.song_state.add_cue.label, "Add Cue");
      lv_obj_set_style_text_font(ui.bottom_band.song_state.add_cue.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.bottom_band.song_state.add_cue.label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.bottom_band.song_state.add_cue.label, LV_ALIGN_CENTER, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.song_state.add_cue.label, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.add_cue.label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.bottom_band.song_state.add_cue.label, LV_OBJ_FLAG_EVENT_BUBBLE);

     


      

      // Container position (ajouté en bas à droite)
      ui.bottom_band.position.container = lv_obj_create(ui.bottom_band.band);
      lv_obj_set_size(ui.bottom_band.position.container, lv_pct(20), lv_pct(30)); // Ajustez la taille si nécessaire
      lv_obj_align(ui.bottom_band.position.container, LV_ALIGN_BOTTOM_RIGHT, -DEFAUTPADDING, -DEFAUTPADDING); // Position en bas à droite
      lv_obj_set_style_bg_opa(ui.bottom_band.position.container, LV_OPA_COVER, 0); // Fond transparent
      lv_obj_set_style_bg_color(ui.bottom_band.position.container, COULEUR_GRIS_FONCE, 0); // Couleur de fond
      lv_obj_set_style_border_width(ui.bottom_band.position.container, 0, 0); // Pas de bordure
      lv_obj_add_style(ui.bottom_band.position.container, &style_shadow, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.position.container, 15, 0);      
      lv_obj_clear_flag(ui.bottom_band.position.container, LV_OBJ_FLAG_SCROLLABLE);

      // Container pour les labels de position
      // Stocké dans ui pour uniformiser
      ui.bottom_band.position.bar_beat_subdiv.container = lv_obj_create(ui.bottom_band.position.container);
      lv_obj_set_size(ui.bottom_band.position.bar_beat_subdiv.container, lv_pct(100), LV_SIZE_CONTENT);
      lv_obj_align(ui.bottom_band.position.bar_beat_subdiv.container, LV_ALIGN_TOP_LEFT, 0,0);
      lv_obj_set_style_bg_opa(ui.bottom_band.position.bar_beat_subdiv.container, LV_OPA_0, 0);     
      lv_obj_set_style_border_width(ui.bottom_band.position.bar_beat_subdiv.container, 0, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.position.bar_beat_subdiv.container, 0, 0);      
      lv_obj_clear_flag(ui.bottom_band.position.bar_beat_subdiv.container, LV_OBJ_FLAG_SCROLLABLE);

      // Label Bar
      ui.bottom_band.position.bar_beat_subdiv.barLabel = lv_label_create(ui.bottom_band.position.bar_beat_subdiv.container);
      lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.barLabel, "1");
      lv_obj_set_style_text_font(ui.bottom_band.position.bar_beat_subdiv.barLabel, POLICE_COUNTER, 0);
      lv_obj_set_style_text_color(ui.bottom_band.position.bar_beat_subdiv.barLabel, COULEUR_BEIGE, 0);
      lv_obj_align(ui.bottom_band.position.bar_beat_subdiv.barLabel, LV_ALIGN_TOP_LEFT, 0, 5);
      lv_obj_set_style_text_align(ui.bottom_band.position.bar_beat_subdiv.barLabel, LV_TEXT_ALIGN_RIGHT, 0);
      lv_obj_set_style_min_width(ui.bottom_band.position.bar_beat_subdiv.barLabel, 55, 0);

      // Séparateur "."
      lv_obj_t *dot1 = lv_label_create(ui.bottom_band.position.bar_beat_subdiv.container);
      lv_label_set_text(dot1, ".");
      lv_obj_align(dot1, LV_ALIGN_TOP_LEFT, 55, 5);
      lv_obj_set_style_text_font(dot1, POLICE_COUNTER, 0);
      lv_obj_set_style_text_color(dot1, COULEUR_BEIGE, 0); 

      // Label Beat
      ui.bottom_band.position.bar_beat_subdiv.beatLabel = lv_label_create(ui.bottom_band.position.bar_beat_subdiv.container);
      lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.beatLabel, "1");
      lv_obj_align(ui.bottom_band.position.bar_beat_subdiv.beatLabel, LV_ALIGN_TOP_LEFT, 60,5);
      lv_obj_set_style_text_font(ui.bottom_band.position.bar_beat_subdiv.beatLabel, POLICE_COUNTER, 0);
      lv_obj_set_style_text_color(ui.bottom_band.position.bar_beat_subdiv.beatLabel, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_align(ui.bottom_band.position.bar_beat_subdiv.beatLabel, LV_TEXT_ALIGN_RIGHT, 0);
      lv_obj_set_style_min_width(ui.bottom_band.position.bar_beat_subdiv.beatLabel, 40, 0);

      // Séparateur "."
      lv_obj_t *dot2 = lv_label_create(ui.bottom_band.position.bar_beat_subdiv.container);
      lv_label_set_text(dot2, "."); 
      lv_obj_align(dot2, LV_ALIGN_OUT_RIGHT_MID, 100, 5);
      lv_obj_set_style_text_font(dot2, POLICE_COUNTER, 0);
      lv_obj_set_style_text_color(dot2, COULEUR_BEIGE, 0);

      // Label Subdivision
      ui.bottom_band.position.bar_beat_subdiv.subdivLabel = lv_label_create(ui.bottom_band.position.bar_beat_subdiv.container);
      lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, "1");
      lv_obj_align(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, LV_ALIGN_TOP_LEFT, 105, 5);
      lv_obj_set_style_text_font(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, POLICE_COUNTER, 0);
      lv_obj_set_style_text_color(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_align(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, LV_TEXT_ALIGN_RIGHT, 0);
      lv_obj_set_style_min_width(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, 40, 0);

      // Container pour les labels de temps
      ui.bottom_band.position.time.container = lv_obj_create(ui.bottom_band.position.container);
      lv_obj_set_size(ui.bottom_band.position.time.container, lv_pct(100), LV_SIZE_CONTENT);
      lv_obj_set_style_bg_opa(ui.bottom_band.position.time.container, LV_OPA_0, 0);     
      lv_obj_set_style_border_width(ui.bottom_band.position.time.container, 0, 0);
      lv_obj_set_style_pad_all(ui.bottom_band.position.time.container, 0, 0);   
      lv_obj_align(ui.bottom_band.position.time.container, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
      lv_obj_clear_flag(ui.bottom_band.position.time.container, LV_OBJ_FLAG_SCROLLABLE);

      // Label Minutes
      ui.bottom_band.position.time.minutesLabel = lv_label_create(ui.bottom_band.position.time.container);
      lv_label_set_text(ui.bottom_band.position.time.minutesLabel, "0");
      lv_obj_align(ui.bottom_band.position.time.minutesLabel, LV_ALIGN_TOP_LEFT, 0, 0);
      lv_obj_set_style_text_font(ui.bottom_band.position.time.minutesLabel, POLICE_TIMER, 0);
      lv_obj_set_style_text_color(ui.bottom_band.position.time.minutesLabel, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_align(ui.bottom_band.position.time.minutesLabel, LV_TEXT_ALIGN_RIGHT, 0);
      lv_obj_set_style_min_width(ui.bottom_band.position.time.minutesLabel, 55, 0);

      // Label "min"
      lv_obj_t *min_label = lv_label_create(ui.bottom_band.position.time.container);
      lv_label_set_text(min_label, " min ");
      lv_obj_align(min_label, LV_ALIGN_TOP_LEFT, 55, 0);
      lv_obj_set_style_text_font(min_label, POLICE_TIMER, 0);
      lv_obj_set_style_text_color(min_label, COULEUR_BEIGE, 0);

      // Label Secondes
      ui.bottom_band.position.time.secondsLabel = lv_label_create(ui.bottom_band.position.time.container);
      lv_label_set_text(ui.bottom_band.position.time.secondsLabel, "0");
      lv_obj_align(ui.bottom_band.position.time.secondsLabel, LV_ALIGN_TOP_LEFT, 90, 0);
      lv_obj_set_style_text_font(ui.bottom_band.position.time.secondsLabel, POLICE_TIMER, 0);
      lv_obj_set_style_text_color(ui.bottom_band.position.time.secondsLabel, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_align(ui.bottom_band.position.time.secondsLabel, LV_TEXT_ALIGN_RIGHT, 0);
      lv_obj_set_style_min_width(ui.bottom_band.position.time.secondsLabel, 40, 0);

      // Label "s"
      lv_obj_t *sec_label = lv_label_create(ui.bottom_band.position.time.container);
      lv_label_set_text(sec_label, " s");
      lv_obj_align(sec_label, LV_ALIGN_TOP_LEFT, 130, 0);
      lv_obj_set_style_text_font(sec_label, POLICE_TIMER, 0);
      lv_obj_set_style_text_color(sec_label, COULEUR_BEIGE, 0);

}

void create_top_band(void) {
 // Création de la bande du haut (sends)
      ui.top_band.band = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.top_band.band, lv_pct(100 - LEFTBANDWIDTHINPERCENT), lv_pct(25));
      lv_obj_align(ui.top_band.band, LV_ALIGN_TOP_RIGHT, 0, 0);
      lv_obj_add_style(ui.top_band.band, &style_fondslider, 0);

      // Création des 4 containers de sends
      for (int i = 0; i < 4; i++)
      {
            create_send_container(i, ui.top_band.band, "", "");
            lv_obj_align(ui.top_band.sends[i].container, LV_ALIGN_LEFT_MID, lv_pct(25 * i), 0);
      }

}

void create_relatives_container(void) {
      // Création de la bande des relatives
      ui.relatives_band.band = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.relatives_band.band, lv_pct(65), lv_pct(30));
      lv_obj_align(ui.relatives_band.band, LV_ALIGN_TOP_LEFT, lv_pct(15), lv_pct(30));
      lv_obj_set_style_bg_opa(ui.relatives_band.band, LV_OPA_0, 0);
      lv_obj_set_style_border_width(ui.relatives_band.band, 0, 0);
      lv_obj_clear_flag(ui.relatives_band.band, LV_OBJ_FLAG_SCROLLABLE);

      ui.relatives_band.parent_container = lv_obj_create(ui.relatives_band.band);
      lv_obj_set_size(ui.relatives_band.parent_container, lv_pct(17), lv_pct(100));
      lv_obj_align(ui.relatives_band.parent_container, LV_ALIGN_LEFT_MID, lv_pct(33), 0);
      lv_obj_set_style_bg_opa(ui.relatives_band.parent_container, LV_OPA_100, 0);
      lv_obj_set_style_bg_color(ui.relatives_band.parent_container, COULEUR_GRIS_MOYEN, 0);
      lv_obj_set_style_border_width(ui.relatives_band.parent_container, 0, 0);
      lv_obj_clear_flag(ui.relatives_band.parent_container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(ui.relatives_band.parent_container, &style_shadow, 0);
      lv_obj_add_flag(ui.relatives_band.parent_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.relatives_band.parent_container, parent_container_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.relatives_band.parent_label = lv_label_create(ui.relatives_band.parent_container);
      lv_label_set_text(ui.relatives_band.parent_label, "In group\n Track");
      lv_obj_set_style_text_line_space(ui.relatives_band.parent_label, 8, LV_PART_MAIN | LV_STATE_DEFAULT); 
      lv_obj_set_style_text_color(ui.relatives_band.parent_label, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_font(ui.relatives_band.parent_label, POLICE_14, 0);
      lv_obj_align(ui.relatives_band.parent_label, LV_ALIGN_LEFT_MID, -9, 20);
      lv_obj_add_flag(ui.relatives_band.parent_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.relatives_band.parent_label, LV_OBJ_FLAG_EVENT_BUBBLE);

      ui.relatives_band.sisters_container = lv_obj_create(ui.relatives_band.band);
      lv_obj_set_size(ui.relatives_band.sisters_container, lv_pct(17), lv_pct(100));
      lv_obj_align(ui.relatives_band.sisters_container, LV_ALIGN_LEFT_MID, lv_pct(53), 0);
      lv_obj_set_style_bg_opa(ui.relatives_band.sisters_container, LV_OPA_100, 0);
      lv_obj_set_style_bg_color(ui.relatives_band.sisters_container, COULEUR_GRIS_CLAIR, 0);
      lv_obj_set_style_border_width(ui.relatives_band.sisters_container, 0, 0);
      lv_obj_clear_flag(ui.relatives_band.sisters_container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(ui.relatives_band.sisters_container, &style_shadow, 0);
      lv_obj_add_flag(ui.relatives_band.sisters_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.relatives_band.sisters_container, sisters_container_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.relatives_band.sisters_label = lv_label_create(ui.relatives_band.sisters_container);
      lv_label_set_text(ui.relatives_band.sisters_label, "Sisters\nTracks");
      lv_obj_set_style_text_line_space(ui.relatives_band.sisters_label, 8, LV_PART_MAIN | LV_STATE_DEFAULT); 
      lv_obj_set_style_text_color(ui.relatives_band.sisters_label, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_font(ui.relatives_band.sisters_label, POLICE_14, 0);
      lv_obj_align(ui.relatives_band.sisters_label, LV_ALIGN_LEFT_MID, -9, 20);
      lv_obj_add_flag(ui.relatives_band.sisters_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.relatives_band.sisters_label, LV_OBJ_FLAG_EVENT_BUBBLE);

      ui.relatives_band.daughter_container = lv_obj_create(ui.relatives_band.band);
      lv_obj_set_size(ui.relatives_band.daughter_container, lv_pct(17), lv_pct(100));
      lv_obj_align(ui.relatives_band.daughter_container, LV_ALIGN_LEFT_MID, lv_pct(73), 0);
      lv_obj_set_style_bg_opa(ui.relatives_band.daughter_container, LV_OPA_100, 0);
      lv_obj_set_style_bg_color(ui.relatives_band.daughter_container, COULEUR_GRIS_TRES_CLAIR, 0);
      lv_obj_set_style_border_width(ui.relatives_band.daughter_container, 0, 0);
      lv_obj_clear_flag(ui.relatives_band.daughter_container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(ui.relatives_band.daughter_container, &style_shadow, 0);
      lv_obj_add_flag(ui.relatives_band.daughter_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.relatives_band.daughter_container, daughters_container_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.relatives_band.daughter_label = lv_label_create(ui.relatives_band.daughter_container);
      lv_label_set_text(ui.relatives_band.daughter_label, "Daughters\n  Tracks");
      lv_obj_set_style_text_line_space(ui.relatives_band.daughter_label, 8, LV_PART_MAIN | LV_STATE_DEFAULT); 
      lv_obj_set_style_text_color(ui.relatives_band.daughter_label, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_font(ui.relatives_band.daughter_label, POLICE_14, 0);
      lv_obj_align(ui.relatives_band.daughter_label, LV_ALIGN_LEFT_MID, -9, 20);
      lv_obj_add_flag(ui.relatives_band.daughter_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(ui.relatives_band.daughter_label, LV_OBJ_FLAG_EVENT_BUBBLE);

}

void create_info_container(void) {
       // Container d'information
       ui.middle_band.container = lv_obj_create(ui.screen);
       lv_obj_set_size(ui.middle_band.container, lv_pct(85), lv_pct(15));
       lv_obj_align(ui.middle_band.container, LV_ALIGN_TOP_LEFT, DECALAGEMODESELECTED, lv_pct(25));
       lv_obj_set_style_radius(ui.middle_band.container, 15, 0);
       lv_obj_set_style_bg_color(ui.middle_band.container, COULEUR_GRIS_FONCE, 0);
       lv_obj_set_style_bg_opa(ui.middle_band.container, LV_OPA_COVER, 0);
       lv_obj_set_style_border_width(ui.middle_band.container, 0, 0);
       lv_obj_clear_flag(ui.middle_band.container, LV_OBJ_FLAG_SCROLLABLE);
       lv_obj_add_style(ui.middle_band.container, &style_shadow, 0);
       lv_obj_set_scrollbar_mode(ui.middle_band.container, LV_SCROLLBAR_MODE_OFF);
 
       // Label pour le mode de piste
       ui.middle_band.track_mode_label = lv_label_create(ui.middle_band.container);
       lv_obj_set_style_text_font(ui.middle_band.track_mode_label, POLICE_18, 0);
       lv_label_set_text(ui.middle_band.track_mode_label, "track mode.");
       lv_obj_set_style_text_color(ui.middle_band.track_mode_label, COULEUR_BEIGE, 0);
       lv_obj_align(ui.middle_band.track_mode_label, LV_ALIGN_LEFT_MID, DEFAUTPADDING, 0);
      
       // Container pour la décoration
       ui.middle_band.decoration_container = lv_obj_create(ui.middle_band.container);
       lv_obj_set_size(ui.middle_band.decoration_container, lv_pct(45), lv_pct(100));    
       lv_obj_set_style_bg_color(ui.middle_band.decoration_container, COULEUR_BEIGE, 0);
       lv_obj_set_style_bg_opa(ui.middle_band.decoration_container, LV_OPA_COVER, 0);
       lv_obj_set_style_border_width(ui.middle_band.decoration_container, 0, 0);
       lv_obj_clear_flag(ui.middle_band.decoration_container, LV_OBJ_FLAG_SCROLLABLE);
       lv_obj_align_to(ui.middle_band.decoration_container, ui.middle_band.track_mode_label, LV_ALIGN_OUT_RIGHT_MID, DEFAUTPADDING * 3, 0);

       // Label pour le nom de la piste
       ui.middle_band.track_name_label = lv_label_create(ui.middle_band.container);
       lv_obj_set_style_text_font(ui.middle_band.track_name_label, POLICE_18, 0);
       lv_label_set_text(ui.middle_band.track_name_label, "Track name");
       lv_obj_set_width(ui.middle_band.track_name_label, lv_pct(45));
       lv_label_set_long_mode(ui.middle_band.track_name_label, LV_LABEL_LONG_CLIP);
       lv_obj_set_style_text_color(ui.middle_band.track_name_label, COULEUR_GRIS_FONCE, 0);
       lv_obj_align_to(ui.middle_band.track_name_label, ui.middle_band.track_mode_label, LV_ALIGN_OUT_RIGHT_MID, DEFAUTPADDING * 4, 0);
 
       // puce pour la couleur de piste
       ui.middle_band.track_color_dot = lv_obj_create(ui.screen);
       lv_obj_set_size(ui.middle_band.track_color_dot, 110, 110);                                                   // Taille du cercle
       lv_obj_align_to(ui.middle_band.track_color_dot, ui.middle_band.container, LV_ALIGN_OUT_RIGHT_MID, -70, 7); // Aligné à droite avec un décalage de 20px
       lv_obj_set_style_bg_color(ui.middle_band.track_color_dot, lv_color_make(255, 255, 255), 0);
       lv_obj_set_style_radius(ui.middle_band.track_color_dot, LV_RADIUS_CIRCLE, 0);
       lv_obj_set_style_border_width(ui.middle_band.track_color_dot, 8, 0);
       lv_obj_set_style_border_color(ui.middle_band.track_color_dot, COULEUR_GRIS_FONCE, 0);
       lv_obj_clear_flag(ui.middle_band.track_color_dot, LV_OBJ_FLAG_SCROLLABLE); // Enlève les scrollbars
       lv_obj_add_style(ui.middle_band.track_color_dot, &style_shadow, 0);
 
 
       // Label pour le numéro de piste
       ui.middle_band.track_number_label = lv_label_create(ui.screen);
       lv_obj_set_style_text_font(ui.middle_band.track_number_label, POLICE_14, 0);
       lv_label_set_text(ui.middle_band.track_number_label, "test");
       lv_obj_set_style_text_color(ui.middle_band.track_number_label, COULEUR_BEIGE, 0);
       lv_obj_set_width(ui.middle_band.track_number_label, 135);
       // Ajoutez ceci pour aligner le texte à droite dans le label
       lv_obj_set_style_text_align(ui.middle_band.track_number_label, LV_TEXT_ALIGN_RIGHT, 0);
       // Alignement à droite de la puce + décalage
       lv_obj_align_to(ui.middle_band.track_number_label, ui.middle_band.track_color_dot, LV_ALIGN_OUT_LEFT_MID, -20, -10);
}

// Function to create the loop length popup
void create_loop_length_popup(void) {
      // Create the background container that covers the entire screen
      ui.loop_length_popup.background_container = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.loop_length_popup.background_container, lv_pct(100), lv_pct(100));
      lv_obj_align(ui.loop_length_popup.background_container, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_style_bg_color(ui.loop_length_popup.background_container, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_opa(ui.loop_length_popup.background_container, LV_OPA_50, 0);
      lv_obj_set_style_border_width(ui.loop_length_popup.background_container, 0, 0);
      lv_obj_add_flag(ui.loop_length_popup.background_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.loop_length_popup.background_container, loop_length_popup_background_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      // Create the content container for the entire popup content
      ui.loop_length_popup.content_container = lv_obj_create(ui.loop_length_popup.background_container);
      lv_obj_set_size(ui.loop_length_popup.content_container, lv_pct(70), lv_pct(70));
      lv_obj_align(ui.loop_length_popup.content_container, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_style_bg_color(ui.loop_length_popup.content_container, COULEUR_GRIS_MOYEN, 0);
      lv_obj_set_style_radius(ui.loop_length_popup.content_container, 10, 0);
      lv_obj_set_style_border_width(ui.loop_length_popup.content_container, 0, 0);
      lv_obj_add_style(ui.loop_length_popup.content_container, &style_shadow, 0);
      lv_obj_set_flex_flow(ui.loop_length_popup.content_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(ui.loop_length_popup.content_container, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
      lv_obj_set_style_pad_all(ui.loop_length_popup.content_container, 20, 0);

      // Create the title label inside content_container
      ui.loop_length_popup.title_label = lv_label_create(ui.loop_length_popup.content_container);
      lv_label_set_text(ui.loop_length_popup.title_label, "Loop Length");
      lv_obj_set_style_text_font(ui.loop_length_popup.title_label, POLICE_20, 0);
      lv_obj_set_style_text_color(ui.loop_length_popup.title_label, COULEUR_BEIGE, 0);

      // Create a container for the rollers
      lv_obj_t *rollers_container = lv_obj_create(ui.loop_length_popup.content_container);
      lv_obj_set_size(rollers_container, lv_pct(100), LV_SIZE_CONTENT);
      lv_obj_set_style_bg_opa(rollers_container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(rollers_container, 0, 0);
      lv_obj_set_flex_flow(rollers_container, LV_FLEX_FLOW_ROW);
      lv_obj_set_flex_align(rollers_container, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
      lv_obj_set_style_pad_all(rollers_container, 0, 0);

      // Create Bar section
      lv_obj_t *bar_container = lv_obj_create(rollers_container);
      lv_obj_set_size(bar_container, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_bg_opa(bar_container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(bar_container, 0, 0);
      lv_obj_set_flex_flow(bar_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(bar_container, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

      ui.loop_length_popup.bar.label = lv_label_create(bar_container);
      lv_label_set_text(ui.loop_length_popup.bar.label, "Bar");
      lv_obj_set_style_text_font(ui.loop_length_popup.bar.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_length_popup.bar.label, COULEUR_BEIGE, 0);

      ui.loop_length_popup.bar.roller = lv_roller_create(bar_container);
      // Créer les options de 0 à 64
      char bar_options[1000] = "0";
      for (int i = 1; i <= 64; i++) {
            char temp[10];
            snprintf(temp, sizeof(temp), "\n%d", i);
            strcat(bar_options, temp);
      }
      lv_roller_set_options(ui.loop_length_popup.bar.roller, bar_options, LV_ROLLER_MODE_NORMAL);
      lv_roller_set_visible_row_count(ui.loop_length_popup.bar.roller, 4);
      lv_roller_set_selected(ui.loop_length_popup.bar.roller, 1, LV_ANIM_OFF); // Valeur initiale 1
      lv_obj_add_event_cb(ui.loop_length_popup.bar.roller, loop_length_roller_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

      // Create Beat section
      lv_obj_t *beat_container = lv_obj_create(rollers_container);
      lv_obj_set_size(beat_container, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_bg_opa(beat_container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(beat_container, 0, 0);
      lv_obj_set_flex_flow(beat_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(beat_container, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

      ui.loop_length_popup.beat.label = lv_label_create(beat_container);
      lv_label_set_text(ui.loop_length_popup.beat.label, "Beat");
      lv_obj_set_style_text_font(ui.loop_length_popup.beat.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_length_popup.beat.label, COULEUR_BEIGE, 0);

      ui.loop_length_popup.beat.roller = lv_roller_create(beat_container);
      // Créer les options de 0 à 16
      char beat_options[200] = "0";
      for (int i = 1; i <= 16; i++) {
            char temp[10];
            snprintf(temp, sizeof(temp), "\n%d", i);
            strcat(beat_options, temp);
      }
      lv_roller_set_options(ui.loop_length_popup.beat.roller, beat_options, LV_ROLLER_MODE_NORMAL);
      lv_roller_set_visible_row_count(ui.loop_length_popup.beat.roller, 4);
      lv_roller_set_selected(ui.loop_length_popup.beat.roller, 0, LV_ANIM_OFF); // Valeur initiale 0
      lv_obj_add_event_cb(ui.loop_length_popup.beat.roller, loop_length_roller_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

      // Create Subdivision section
      lv_obj_t *subdivision_container = lv_obj_create(rollers_container);
      lv_obj_set_size(subdivision_container, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_bg_opa(subdivision_container, LV_OPA_0, 0);
      lv_obj_set_style_border_width(subdivision_container, 0, 0);
      lv_obj_set_flex_flow(subdivision_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(subdivision_container, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

      ui.loop_length_popup.subdivision.label = lv_label_create(subdivision_container);
      lv_label_set_text(ui.loop_length_popup.subdivision.label, "Subdivision");
      lv_obj_set_style_text_font(ui.loop_length_popup.subdivision.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_length_popup.subdivision.label, COULEUR_BEIGE, 0);

      ui.loop_length_popup.subdivision.roller = lv_roller_create(subdivision_container);
      // Créer les options de 0 à 16
      char subdivision_options[200] = "0";
      for (int i = 1; i <= 16; i++) {
            char temp[10];
            snprintf(temp, sizeof(temp), "\n%d", i);
            strcat(subdivision_options, temp);
      }
      lv_roller_set_options(ui.loop_length_popup.subdivision.roller, subdivision_options, LV_ROLLER_MODE_NORMAL);
      lv_roller_set_visible_row_count(ui.loop_length_popup.subdivision.roller, 4);
      lv_roller_set_selected(ui.loop_length_popup.subdivision.roller, 0, LV_ANIM_OFF); // Valeur initiale 0
      lv_obj_add_event_cb(ui.loop_length_popup.subdivision.roller, loop_length_roller_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

      // Create OK button inside content_container
      ui.loop_length_popup.ok_button = lv_obj_create(ui.loop_length_popup.content_container);
      lv_obj_set_size(ui.loop_length_popup.ok_button, 80, 40);
      lv_obj_set_style_bg_color(ui.loop_length_popup.ok_button, COULEUR_VERT, 0);
      lv_obj_set_style_radius(ui.loop_length_popup.ok_button, 5, 0);
      lv_obj_set_style_border_width(ui.loop_length_popup.ok_button, 0, 0);
      lv_obj_add_style(ui.loop_length_popup.ok_button, &style_shadow, 0);
      lv_obj_add_flag(ui.loop_length_popup.ok_button, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_clear_flag(ui.loop_length_popup.ok_button, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_event_cb(ui.loop_length_popup.ok_button, loop_length_ok_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.loop_length_popup.ok_label = lv_label_create(ui.loop_length_popup.ok_button);
      lv_label_set_text(ui.loop_length_popup.ok_label, "OK");
      lv_obj_set_style_text_font(ui.loop_length_popup.ok_label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_length_popup.ok_label, COULEUR_BEIGE, 0);
      lv_obj_center(ui.loop_length_popup.ok_label);

      // Initially hide the popup
      lv_obj_add_flag(ui.loop_length_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}

// Function to create the cues popup
void create_cues_popup(void) {
      // Create the background container that covers the entire screen
      ui.cues_popup.background_container = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.cues_popup.background_container, lv_pct(100), lv_pct(100));
      lv_obj_align(ui.cues_popup.background_container, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_style_bg_color(ui.cues_popup.background_container, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_opa(ui.cues_popup.background_container, LV_OPA_50, 0);
      lv_obj_set_style_border_width(ui.cues_popup.background_container, 0, 0);
      lv_obj_add_flag(ui.cues_popup.background_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.cues_popup.background_container, cues_popup_background_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      // Create the title label
      ui.cues_popup.title_label = lv_label_create(ui.cues_popup.background_container);
      lv_label_set_text(ui.cues_popup.title_label, "Cues");
      lv_obj_set_style_text_font(ui.cues_popup.title_label, POLICE_20, 0);
      lv_obj_set_style_text_color(ui.cues_popup.title_label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.cues_popup.title_label, LV_ALIGN_TOP_MID, 0, 20);

      // Create the container for the cue buttons
      ui.cues_popup.cues_container = lv_obj_create(ui.cues_popup.background_container);
      lv_obj_set_size(ui.cues_popup.cues_container, lv_pct(40), lv_pct(90));
      lv_obj_align(ui.cues_popup.cues_container, LV_ALIGN_CENTER, 0, 10);
      lv_obj_set_style_bg_color(ui.cues_popup.cues_container, COULEUR_GRIS_MOYEN, 0);
      lv_obj_set_style_radius(ui.cues_popup.cues_container, 10, 0);
      lv_obj_set_style_border_width(ui.cues_popup.cues_container, 0, 0);
      lv_obj_add_style(ui.cues_popup.cues_container, &style_shadow, 0);

      // Set up scrolling for the cues container
      lv_obj_set_flex_flow(ui.cues_popup.cues_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_style_pad_all(ui.cues_popup.cues_container, 10, 0);
      lv_obj_set_style_pad_row(ui.cues_popup.cues_container, 15, 0);
      lv_obj_add_flag(ui.cues_popup.cues_container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_set_scroll_dir(ui.cues_popup.cues_container, LV_DIR_VER);
      lv_obj_set_scrollbar_mode(ui.cues_popup.cues_container, LV_SCROLLBAR_MODE_AUTO);
      lv_obj_set_scroll_snap_y(ui.cues_popup.cues_container, LV_SCROLL_SNAP_NONE);
      lv_obj_add_style(ui.cues_popup.cues_container, &style_scrollbar, LV_PART_SCROLLBAR);

      // Create all 64 cue buttons
      for (int i = 0; i < 64; i++) {
            // Create button
            ui.cues_popup.cue_buttons[i] = lv_obj_create(ui.cues_popup.cues_container);
            lv_obj_set_size(ui.cues_popup.cue_buttons[i], lv_pct(100), 60);
            lv_obj_set_style_bg_color(ui.cues_popup.cue_buttons[i], COULEUR_GRIS, 0);
            lv_obj_set_style_radius(ui.cues_popup.cue_buttons[i], 5, 0);
            lv_obj_set_style_border_width(ui.cues_popup.cue_buttons[i], 2, 0);
            lv_obj_set_style_border_color(ui.cues_popup.cue_buttons[i], COULEUR_GRIS_FONCE, 0);
            lv_obj_add_style(ui.cues_popup.cue_buttons[i], &style_shadow_little, 0);
            lv_obj_clear_flag(ui.cues_popup.cue_buttons[i], LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_add_flag(ui.cues_popup.cue_buttons[i], LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(ui.cues_popup.cue_buttons[i], cue_button_event_cb, LV_EVENT_CLICKED, (void*)(intptr_t)(i));

            // Create label for the button
            ui.cues_popup.cue_labels[i] = lv_label_create(ui.cues_popup.cue_buttons[i]);
            lv_label_set_text_fmt(ui.cues_popup.cue_labels[i], "%d", i + 1);
            lv_obj_set_style_text_font(ui.cues_popup.cue_labels[i], POLICE_14, 0);
            lv_obj_set_style_text_color(ui.cues_popup.cue_labels[i], COULEUR_BEIGE, 0);
            lv_obj_center(ui.cues_popup.cue_labels[i]);

            // Masquer les boutons après le 16ème (index 15)
            if (i >= 0) {
                  lv_obj_add_flag(ui.cues_popup.cue_buttons[i], LV_OBJ_FLAG_HIDDEN);
            }
      }

      // Initially hide the popup
      lv_obj_add_flag(ui.cues_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}

// Function to create the loop settings popup
void create_loop_settings_popup(void) {
      // Create the background container that covers the entire screen
      ui.loop_settings_popup.background_container = lv_obj_create(ui.screen);
      lv_obj_set_size(ui.loop_settings_popup.background_container, lv_pct(100), lv_pct(100));
      lv_obj_align(ui.loop_settings_popup.background_container, LV_ALIGN_CENTER, 0, 0);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.background_container, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_bg_opa(ui.loop_settings_popup.background_container, LV_OPA_50, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.background_container, 0, 0);
      lv_obj_add_flag(ui.loop_settings_popup.background_container, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(ui.loop_settings_popup.background_container, loop_settings_popup_background_event_cb, LV_EVENT_CLICKED, NULL);

      // Create the main content container (plus grand pour 4 boutons)
      ui.loop_settings_popup.content_container = lv_obj_create(ui.loop_settings_popup.background_container);
      lv_obj_set_size(ui.loop_settings_popup.content_container, 300, 280);
      lv_obj_center(ui.loop_settings_popup.content_container);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.content_container, COULEUR_GRIS, 0);
      lv_obj_set_style_border_color(ui.loop_settings_popup.content_container, COULEUR_BEIGE, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.content_container, 2, 0);
      lv_obj_set_style_radius(ui.loop_settings_popup.content_container, 10, 0);

      // Create the title label
      ui.loop_settings_popup.title_label = lv_label_create(ui.loop_settings_popup.content_container);
      lv_label_set_text(ui.loop_settings_popup.title_label, "Loop Settings");
      lv_obj_set_style_text_font(ui.loop_settings_popup.title_label, POLICE_16, 0);
      lv_obj_set_style_text_color(ui.loop_settings_popup.title_label, COULEUR_BEIGE, 0);
      lv_obj_align(ui.loop_settings_popup.title_label, LV_ALIGN_TOP_MID, 0, 20);

      // Create "Set loop start here" button
      ui.loop_settings_popup.set_loop_start.btn = lv_btn_create(ui.loop_settings_popup.content_container);
      lv_obj_set_size(ui.loop_settings_popup.set_loop_start.btn, 250, 35);
      lv_obj_align(ui.loop_settings_popup.set_loop_start.btn, LV_ALIGN_CENTER, 0, -60);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.set_loop_start.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_border_color(ui.loop_settings_popup.set_loop_start.btn, COULEUR_BEIGE, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.set_loop_start.btn, 1, 0);
      lv_obj_set_style_radius(ui.loop_settings_popup.set_loop_start.btn, 5, 0);
      lv_obj_add_event_cb(ui.loop_settings_popup.set_loop_start.btn, loop_settings_set_start_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.loop_settings_popup.set_loop_start.label = lv_label_create(ui.loop_settings_popup.set_loop_start.btn);
      lv_label_set_text(ui.loop_settings_popup.set_loop_start.label, "Set loop start here");
      lv_obj_set_style_text_font(ui.loop_settings_popup.set_loop_start.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_settings_popup.set_loop_start.label, COULEUR_BEIGE, 0);
      lv_obj_center(ui.loop_settings_popup.set_loop_start.label);

      // Create "Set loop length" button
      ui.loop_settings_popup.set_loop_length.btn = lv_btn_create(ui.loop_settings_popup.content_container);
      lv_obj_set_size(ui.loop_settings_popup.set_loop_length.btn, 250, 35);
      lv_obj_align(ui.loop_settings_popup.set_loop_length.btn, LV_ALIGN_CENTER, 0, -15);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.set_loop_length.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_border_color(ui.loop_settings_popup.set_loop_length.btn, COULEUR_BEIGE, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.set_loop_length.btn, 1, 0);
      lv_obj_set_style_radius(ui.loop_settings_popup.set_loop_length.btn, 5, 0);
      lv_obj_add_event_cb(ui.loop_settings_popup.set_loop_length.btn, loop_settings_set_length_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.loop_settings_popup.set_loop_length.label = lv_label_create(ui.loop_settings_popup.set_loop_length.btn);
      lv_label_set_text(ui.loop_settings_popup.set_loop_length.label, "Set loop length");
      lv_obj_set_style_text_font(ui.loop_settings_popup.set_loop_length.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_settings_popup.set_loop_length.label, COULEUR_BEIGE, 0);
      lv_obj_center(ui.loop_settings_popup.set_loop_length.label);

      // Create "Punch In" button
      ui.loop_settings_popup.punch_in.btn = lv_btn_create(ui.loop_settings_popup.content_container);
      lv_obj_set_size(ui.loop_settings_popup.punch_in.btn, 250, 35);
      lv_obj_align(ui.loop_settings_popup.punch_in.btn, LV_ALIGN_CENTER, 0, 30);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.punch_in.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_border_color(ui.loop_settings_popup.punch_in.btn, COULEUR_BEIGE, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.punch_in.btn, 1, 0);
      lv_obj_set_style_radius(ui.loop_settings_popup.punch_in.btn, 5, 0);
      lv_obj_add_event_cb(ui.loop_settings_popup.punch_in.btn, loop_settings_punch_in_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.loop_settings_popup.punch_in.label = lv_label_create(ui.loop_settings_popup.punch_in.btn);
      lv_label_set_text(ui.loop_settings_popup.punch_in.label, "Punch In");
      lv_obj_set_style_text_font(ui.loop_settings_popup.punch_in.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_settings_popup.punch_in.label, COULEUR_BEIGE, 0);
      lv_obj_center(ui.loop_settings_popup.punch_in.label);

      // Create "Punch Out" button
      ui.loop_settings_popup.punch_out.btn = lv_btn_create(ui.loop_settings_popup.content_container);
      lv_obj_set_size(ui.loop_settings_popup.punch_out.btn, 250, 35);
      lv_obj_align(ui.loop_settings_popup.punch_out.btn, LV_ALIGN_CENTER, 0, 75);
      lv_obj_set_style_bg_color(ui.loop_settings_popup.punch_out.btn, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_border_color(ui.loop_settings_popup.punch_out.btn, COULEUR_BEIGE, 0);
      lv_obj_set_style_border_width(ui.loop_settings_popup.punch_out.btn, 1, 0);
      lv_obj_set_style_radius(ui.loop_settings_popup.punch_out.btn, 5, 0);
      lv_obj_add_event_cb(ui.loop_settings_popup.punch_out.btn, loop_settings_punch_out_button_event_cb, LV_EVENT_SHORT_CLICKED, NULL);

      ui.loop_settings_popup.punch_out.label = lv_label_create(ui.loop_settings_popup.punch_out.btn);
      lv_label_set_text(ui.loop_settings_popup.punch_out.label, "Punch Out");
      lv_obj_set_style_text_font(ui.loop_settings_popup.punch_out.label, POLICE_14, 0);
      lv_obj_set_style_text_color(ui.loop_settings_popup.punch_out.label, COULEUR_BEIGE, 0);
      lv_obj_center(ui.loop_settings_popup.punch_out.label);

      // Initially hide the popup
      lv_obj_add_flag(ui.loop_settings_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}