#include "lvgl.h"
#include "styles.h"
#include "esp_lvgl_port.h"

#define PADDING 10

/* // Définition de la couleur réutilisable
#define COULEUR_BEIGE lv_color_make(245, 245, 220)
#define COULEUR_GRIS lv_color_make(64, 64, 64)
#define COULEUR_GRIS_FONCE lv_color_make(32, 32, 32)
#define COULEUR_NOIRE lv_color_make(0, 0, 0)
#define COULEUR_ORANGE lv_color_make(255, 173, 86)
#define COULEUR_BLEU lv_color_make(60, 106, 182) */

lv_style_t style_container;
lv_style_t style_container_unselected;
lv_style_t style_container_selected;

lv_style_t style_leftband;
lv_style_t style_middleband;
lv_style_t style_fondslider;

lv_style_t style_bar_bg;
lv_style_t style_bar_indic;

lv_style_t style_container_label;

lv_style_t style_btn_normal;
lv_style_t style_btn_pressed;
lv_style_t style_btn_external;

lv_style_t style_mode;
lv_style_t style_mode_active;
lv_style_t style_mode_others;

lv_style_t style_unselected_device;
lv_style_t style_selected_device;
lv_style_t style_device;
lv_style_t style_device_pressed;

lv_style_t style_device_button;
lv_style_t style_device_button_pressed;
lv_style_t style_device_button_disabled;

// Déclaration des nouvelles variables de style
lv_style_t style_device_normal;
lv_style_t style_device_collapsed;
lv_style_t style_device_noncollapsed;

lv_style_t style_arc_pan_main;
lv_style_t style_arc_pan_indicator;

lv_style_t style_scrollbar;

lv_style_t style_shadow;
lv_style_t style_shadow_little;

void init_styles(void)
{
      // Style pour les containers
      lv_style_init(&style_container);
      lv_style_set_border_width(&style_container, 0);
      lv_style_set_pad_all(&style_container, PADDING);
      lv_style_set_radius(&style_container, 15);
      lv_style_set_pad_all(&style_container, PADDING);
      lv_style_set_bg_color(&style_container, COULEUR_GRIS);
      lv_style_set_outline_width(&style_container, 0);
      // lv_style_set_outline_color(&style_container, COULEUR_BEIGE);

      // Style pour les containers non sélectionnés
      lv_style_init(&style_container_unselected);
      lv_style_set_bg_color(&style_container_unselected, COULEUR_GRIS);

      // Style pour les containers sélectionnés
      lv_style_init(&style_container_selected);
      lv_style_set_bg_color(&style_container_selected, COULEUR_GRIS_CLAIR);

      // Style pour la bande de gauche
      lv_style_init(&style_leftband);
      lv_style_set_bg_color(&style_leftband, COULEUR_BEIGE_JAUNE);
      lv_style_set_bg_opa(&style_leftband, LV_OPA_COVER);
      lv_style_set_radius(&style_leftband, 0);
      lv_style_set_border_width(&style_leftband, 0);
      lv_style_set_pad_all(&style_leftband, 10);

      // Style pour la bande du milieu
      lv_style_init(&style_middleband);
      lv_style_set_bg_color(&style_middleband, COULEUR_GRIS);
      lv_style_set_radius(&style_middleband, 0);
      lv_style_set_border_width(&style_middleband, 0);
      lv_style_set_pad_all(&style_middleband, PADDING);

      // Style pour le fond slider
      lv_style_init(&style_fondslider);
      lv_style_set_bg_color(&style_fondslider, COULEUR_GRIS);
      lv_style_set_radius(&style_fondslider, 0);
      lv_style_set_border_width(&style_fondslider, 0);
      lv_style_set_pad_all(&style_fondslider, PADDING);

      // Style pour le fond de la barre
      lv_style_init(&style_bar_bg);
      lv_style_set_bg_color(&style_bar_bg, lv_color_black());
      lv_style_set_bg_opa(&style_bar_bg, LV_OPA_COVER);
      lv_style_set_border_width(&style_bar_bg, 0);
      lv_style_set_pad_all(&style_bar_bg, 7);
     

      // Style pour les labels des containers
      lv_style_init(&style_container_label);
      lv_style_set_text_color(&style_container_label, COULEUR_BEIGE);
      lv_style_set_text_font(&style_container_label, POLICE_14);
      lv_style_set_width(&style_container_label, lv_pct(100));

      // Style pour l'indicateur de la barre
      lv_style_init(&style_bar_indic);
      lv_style_set_bg_color(&style_bar_indic, COULEUR_BEIGE);

      // Style pour les boutons normaux
      lv_style_init(&style_btn_normal);
      lv_style_set_bg_color(&style_btn_normal, lv_color_make(200, 200, 200));

      // Style pour les boutons pressés
      lv_style_init(&style_btn_pressed);
      lv_style_set_bg_color(&style_btn_pressed, lv_color_make(100, 100, 100));

      // Style pour les boutons externes
      lv_style_init(&style_btn_external);
      lv_style_set_bg_color(&style_btn_external, lv_color_make(0, 255, 0));

      // Style pour Mode
      lv_style_init(&style_mode);
      lv_style_set_text_font(&style_mode, &lv_font_montserrat_20);

      // Style pour ModeActive
      lv_style_init(&style_mode_active);
      lv_style_set_border_width(&style_mode_active, 5);
      lv_style_set_border_color(&style_mode_active, lv_color_black());

      // Style pour ModeOthers
      lv_style_init(&style_mode_others);
      lv_style_set_border_width(&style_mode_others, 0);

      // Style pour les devices non sélectionnés
      lv_style_init(&style_unselected_device);
      lv_style_set_border_width(&style_unselected_device, 0);

      // Style pour les devices sélectionnés
      lv_style_init(&style_selected_device);
      lv_style_set_border_width(&style_selected_device, 4);

      // Style de base pour les devices
      lv_style_init(&style_device);
      lv_style_set_text_color(&style_device, COULEUR_BEIGE);
      lv_style_set_bg_color(&style_device, COULEUR_GRIS);
      lv_style_set_bg_opa(&style_device, LV_OPA_COVER);

      // Style pour l'état pressé des devices
      lv_style_init(&style_device_pressed);
      lv_style_set_bg_color(&style_device_pressed, COULEUR_BEIGE);
      lv_style_set_text_color(&style_device_pressed, COULEUR_GRIS);
      lv_style_set_bg_opa(&style_device_pressed, LV_OPA_COVER);

      // Style pour les boutons de dispositif normaux
      lv_style_init(&style_device_button);
      lv_style_set_bg_color(&style_device_button, COULEUR_GRIS);
      lv_style_set_text_color(&style_device_button, COULEUR_BEIGE);
      lv_style_set_border_width(&style_device_button, 2);
      lv_style_set_border_color(&style_device_button, COULEUR_BEIGE);
      lv_style_set_pad_all(&style_device_button, 10);
      lv_style_set_radius(&style_device_button, 5);

      // Style pour les boutons de dispositif pressés
      lv_style_init(&style_device_button_pressed);
      lv_style_set_bg_color(&style_device_button_pressed, COULEUR_BEIGE);
      lv_style_set_text_color(&style_device_button_pressed, COULEUR_GRIS);
      lv_style_set_border_width(&style_device_button_pressed, 2);
      lv_style_set_border_color(&style_device_button_pressed, COULEUR_GRIS);
      lv_style_set_pad_all(&style_device_button_pressed, 10);
      lv_style_set_radius(&style_device_button_pressed, 5);

      // Style pour les boutons de dispositif désactivés
      lv_style_init(&style_device_button_disabled);
      lv_style_set_bg_color(&style_device_button_disabled, lv_color_make(128, 128, 128));
      lv_style_set_text_color(&style_device_button_disabled, lv_color_make(180, 180, 180));
      lv_style_set_border_width(&style_device_button_disabled, 2);
      lv_style_set_border_color(&style_device_button_disabled, lv_color_make(150, 150, 150));
      lv_style_set_pad_all(&style_device_button_disabled, 10);
      lv_style_set_radius(&style_device_button_disabled, 5);
      lv_style_set_opa(&style_device_button_disabled, LV_OPA_50);

      // Style pour l'état normal des devices
      lv_style_init(&style_device_normal);
      lv_style_set_text_color(&style_device_normal, COULEUR_BEIGE);

      // Style pour les devices repliés
      lv_style_init(&style_device_collapsed);
      lv_style_set_text_color(&style_device_collapsed, lv_color_make(255, 0, 0)); // Rouge

      // Style pour les devices non repliés
      lv_style_init(&style_device_noncollapsed);
      lv_style_set_text_color(&style_device_noncollapsed, lv_color_make(0, 0, 255)); // Bleu

      // Style pour l'arc du pan (partie principale)
      lv_style_init(&style_arc_pan_main);
      lv_style_set_bg_color(&style_arc_pan_main, COULEUR_GRIS);
      lv_style_set_arc_width(&style_arc_pan_main, 20);
      lv_style_set_arc_color(&style_arc_pan_main, lv_color_make(0, 0, 0));
      lv_style_set_arc_rounded(&style_arc_pan_main, true);

      // Style pour l'indicateur de l'arc du pan
      lv_style_init(&style_arc_pan_indicator);
      lv_style_set_arc_color(&style_arc_pan_indicator, COULEUR_BEIGE);
      lv_style_set_arc_width(&style_arc_pan_indicator, 10);
      lv_style_set_pad_all(&style_arc_pan_indicator, 5);
      lv_style_set_arc_rounded(&style_arc_pan_indicator, true);
      lv_style_set_shadow_width(&style_arc_pan_indicator, 5);
      lv_style_set_shadow_color(&style_arc_pan_indicator, lv_color_black());
      lv_style_set_shadow_opa(&style_arc_pan_indicator, LV_OPA_30);
      lv_style_set_shadow_ofs_x(&style_arc_pan_indicator, 3);
      lv_style_set_shadow_ofs_y(&style_arc_pan_indicator, 3);
      lv_style_set_shadow_spread(&style_arc_pan_indicator, 2);

      lv_style_init(&style_scrollbar);
      lv_style_set_width(&style_scrollbar, 5);               // Largeur de la scrollbar
      lv_style_set_bg_color(&style_scrollbar, COULEUR_BEIGE); // Couleur grise
      lv_style_set_bg_opa(&style_scrollbar, LV_OPA_50);      
      lv_style_set_pad_all(&style_scrollbar, 5);

      lv_style_init(&style_shadow);
      lv_style_set_shadow_width(&style_shadow, 10);
      lv_style_set_shadow_color(&style_shadow, lv_color_black());
      lv_style_set_shadow_opa(&style_shadow, LV_OPA_50);
      lv_style_set_shadow_ofs_x(&style_shadow, 5);
      lv_style_set_shadow_ofs_y(&style_shadow, 5);
      lv_style_set_shadow_spread(&style_shadow, 2);

      lv_style_init(&style_shadow_little);
      lv_style_set_shadow_width(&style_shadow_little, 5);
      lv_style_set_shadow_color(&style_shadow_little, lv_color_black());
      lv_style_set_shadow_opa(&style_shadow_little, LV_OPA_50);
      lv_style_set_shadow_ofs_x(&style_shadow_little, 3);
      lv_style_set_shadow_ofs_y(&style_shadow_little, 3);
      lv_style_set_shadow_spread(&style_shadow_little, 2);
}