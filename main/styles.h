#ifndef STYLES_H
#define STYLES_H

#include "lvgl.h"
#include "esp_lvgl_port.h"

LV_FONT_DECLARE(lv_font_jost_14);
LV_FONT_DECLARE(lv_font_jost_16);   
LV_FONT_DECLARE(lv_font_jost_18);
LV_FONT_DECLARE(lv_font_jost_20);
LV_FONT_DECLARE(lv_font_jost_24);
LV_FONT_DECLARE(lv_font_jost_28);
LV_FONT_DECLARE(lv_font_jost_bold_14);
LV_FONT_DECLARE(lv_font_jost_bold_16);
LV_FONT_DECLARE(lv_font_jost_bold_18);
LV_FONT_DECLARE(lv_font_jost_bold_20);
LV_FONT_DECLARE(lv_font_jost_bold_24);
LV_FONT_DECLARE(lv_font_jost_bold_28);


// Définition de la couleur réutilisable
#define COULEUR_BEIGE lv_color_make(220, 220, 185)
#define COULEUR_BEIGE_JAUNE lv_color_make(220, 220, 130)
#define COULEUR_BEIGE_FONCE lv_color_make(245, 245, 220)
#define COULEUR_GRIS lv_color_make(44, 32, 36)
#define COULEUR_GRIS_FONCE lv_color_make(16, 16, 16)
#define COULEUR_NOIRE lv_color_make(0, 0, 0)
#define COULEUR_ORANGE lv_color_make(255, 173, 86)
#define COULEUR_BLEU lv_color_make(60, 106, 182)
#define COULEUR_GRIS_CLAIR lv_color_make(64, 64, 64)
#define COULEUR_GRIS_MOYEN lv_color_make(48, 48, 48)
#define COULEUR_VERT lv_color_make(0, 128, 0)
#define COULEUR_BORDEAUX lv_color_make(60, 20, 20)
#define COULEUR_GRIS_TRES_CLAIR lv_color_make(96, 96, 96)

#define DEFAULT_FONT &lv_font_jost_bold_14
#define POLICE_14 &lv_font_jost_14
#define POLICE_16 &lv_font_jost_16
#define POLICE_18 &lv_font_jost_18
#define POLICE_20 &lv_font_jost_20
#define POLICE_24 &lv_font_jost_24
#define POLICE_28 &lv_font_jost_28
#define POLICE_BOLD_14 &lv_font_jost_bold_14
#define POLICE_BOLD_16 &lv_font_jost_bold_16
#define POLICE_BOLD_18 &lv_font_jost_bold_18
#define POLICE_BOLD_20 &lv_font_jost_bold_20
#define POLICE_BOLD_24 &lv_font_jost_bold_24
#define POLICE_BOLD_28 &lv_font_jost_bold_28

#define LEFTBANDWIDTHINPERCENT 12

#define DECALAGEMODESELECTED lv_pct(9)

// Déclarations des styles externes
extern lv_style_t style_container;
extern lv_style_t style_container_unselected;
extern lv_style_t style_container_selected;
extern lv_style_t style_leftband;
extern lv_style_t style_middleband;
extern lv_style_t style_fondslider;
extern lv_style_t style_container_label;
extern lv_style_t style_bar_bg;
extern lv_style_t style_bar_indic;
extern lv_style_t style_btn_normal;
extern lv_style_t style_btn_pressed;
extern lv_style_t style_btn_external;
extern lv_style_t style_mode;
extern lv_style_t style_mode_active;
extern lv_style_t style_mode_others;
extern lv_style_t style_unselected_device;
extern lv_style_t style_selected_device;
extern lv_style_t style_device;
extern lv_style_t style_device_pressed;
extern lv_style_t style_device_button;
extern lv_style_t style_device_button_pressed;
extern lv_style_t style_device_button_disabled;
extern lv_style_t style_device_normal;
extern lv_style_t style_device_collapsed;
extern lv_style_t style_device_noncollapsed;
extern lv_style_t style_scrollbar;
extern lv_style_t style_shadow;
extern lv_style_t style_shadow_little;

// ====================================
// Styles pour l'écran Track (scr_liv_tra.c)
// ====================================

extern lv_style_t style_arc_pan_main;
extern lv_style_t style_arc_pan_indicator;

// Déclaration de la fonction d'initialisation
void init_styles(void);

#endif // STYLES_H
