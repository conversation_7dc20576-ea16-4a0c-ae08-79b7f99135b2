#include "lvgl.h"
#include "../gui.h"
#include "../styles.h"
#include "scr_liv_vol.h"

#ifdef __cplusplus
extern "C"
{
#endif
      extern void sendMessage(const char *message);
      extern void switch_to_screen(lv_obj_t *screen);
#ifdef __cplusplus
}
#endif

extern const lv_img_dsc_t downarrow;

// Fonction create_container utilisant la notation par points
static void create_container(lv_obj_t *parent, const char *top_label, const char *bottom_label, bool is_top)
{
      static int container_index = 0;
      int actual_index = container_index;

      volume_screen.containers[container_index].container = lv_obj_create(parent);
      lv_obj_set_size(volume_screen.containers[container_index].container, lv_pct(25), lv_pct(100));
      lv_obj_clear_flag(volume_screen.containers[container_index].container, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(volume_screen.containers[container_index].container, &style_container, 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].container, 15, 0);
      lv_obj_add_style(volume_screen.containers[container_index].container, &style_container_unselected, 0);

      // Label du haut
      volume_screen.containers[container_index].top_label = lv_label_create(volume_screen.containers[container_index].container);
      lv_label_set_text(volume_screen.containers[container_index].top_label, top_label);
      lv_label_set_long_mode(volume_screen.containers[container_index].top_label, LV_LABEL_LONG_DOT);
      lv_obj_set_size(volume_screen.containers[container_index].top_label, lv_pct(100), LV_SIZE_CONTENT);
      lv_obj_align(volume_screen.containers[container_index].top_label, LV_ALIGN_TOP_LEFT, 0, 0);
      lv_obj_set_style_text_font(volume_screen.containers[container_index].top_label, POLICE_14, 0);
      lv_obj_set_style_text_color(volume_screen.containers[container_index].top_label, COULEUR_BEIGE, 0);
      lv_obj_set_style_max_height(volume_screen.containers[container_index].top_label, 20, 0);
      lv_obj_align(volume_screen.containers[container_index].top_label, LV_ALIGN_TOP_LEFT, 0, lv_pct(5));

      volume_screen.containers[container_index].color_circle = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].color_circle, lv_pct(30), lv_pct(26));
      lv_obj_set_pos(volume_screen.containers[container_index].color_circle, lv_pct(65), lv_pct(25));
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].color_circle, lv_color_make(128, 0, 0), 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].color_circle, LV_RADIUS_CIRCLE, 0);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].color_circle, 5, 0);
      lv_obj_set_style_border_color(volume_screen.containers[container_index].color_circle, lv_color_black(), 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].color_circle, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(volume_screen.containers[container_index].color_circle, &style_shadow, 0);
      // Barre
      volume_screen.containers[container_index].bar = lv_bar_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].bar, lv_pct(100), 20);
      lv_bar_set_range(volume_screen.containers[container_index].bar, 0, 100);
      lv_bar_set_value(volume_screen.containers[container_index].bar, 0, LV_ANIM_OFF);
      lv_obj_add_style(volume_screen.containers[container_index].bar, &style_bar_bg, LV_PART_MAIN | LV_STATE_DEFAULT);
      lv_obj_add_style(volume_screen.containers[container_index].bar, &style_bar_indic, LV_PART_INDICATOR);
      lv_obj_align(volume_screen.containers[container_index].bar, LV_ALIGN_TOP_LEFT, 0, lv_pct(24));

      // Label du bas
      volume_screen.containers[container_index].bottom_label = lv_label_create(volume_screen.containers[container_index].container);
      lv_label_set_text(volume_screen.containers[container_index].bottom_label, bottom_label);

      lv_obj_set_style_text_color(volume_screen.containers[container_index].bottom_label, COULEUR_BEIGE, 0);
      lv_obj_set_style_text_font(volume_screen.containers[container_index].bottom_label, POLICE_14, 0);
      lv_obj_align(volume_screen.containers[container_index].bottom_label, LV_ALIGN_TOP_LEFT, 0, lv_pct(43));

      // Indicateurs
      volume_screen.containers[container_index].indicators.fold_circle = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].indicators.fold_circle, 25, 25);
      lv_obj_set_pos(volume_screen.containers[container_index].indicators.fold_circle, lv_pct(80), lv_pct(80));
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].indicators.fold_circle, lv_color_make(255, 255, 255), 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].indicators.fold_circle, LV_RADIUS_CIRCLE, 0);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].indicators.fold_circle, 0, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].indicators.fold_circle, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(volume_screen.containers[container_index].indicators.fold_circle, LV_OBJ_FLAG_HIDDEN);

      volume_screen.containers[container_index].indicators.grouped_circle = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].indicators.grouped_circle, 25, 25);
      lv_obj_set_pos(volume_screen.containers[container_index].indicators.grouped_circle, lv_pct(50), lv_pct(80));
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].indicators.grouped_circle, lv_color_make(255, 255, 255), 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].indicators.grouped_circle, LV_RADIUS_CIRCLE, 0);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].indicators.grouped_circle, 0, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].indicators.grouped_circle, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(volume_screen.containers[container_index].indicators.grouped_circle, LV_OBJ_FLAG_HIDDEN);

      // Masque - Correction de l'index et de la position
      volume_screen.containers[container_index].mask = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].mask, lv_pct(100), lv_pct(60));
      lv_obj_set_style_bg_opa(volume_screen.containers[container_index].mask, LV_OPA_0, 0);
      lv_obj_align(volume_screen.containers[container_index].mask, LV_ALIGN_TOP_LEFT, 0, 0);
      lv_obj_add_event_cb(volume_screen.containers[container_index].mask, mask_click_handler, LV_EVENT_SHORT_CLICKED, (void *)(intptr_t)actual_index);
      lv_obj_add_event_cb(volume_screen.containers[container_index].mask, mask_long_pressed_handler, LV_EVENT_LONG_PRESSED, (void *)(intptr_t)actual_index);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].mask, 0, 0);

      // Touchzone - Correction de l'index et de la position
      volume_screen.containers[container_index].mutesolo_touchzone = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].mutesolo_touchzone, lv_pct(50), lv_pct(40));
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].mutesolo_touchzone, lv_color_make(255, 255, 255), 0);
      lv_obj_set_style_bg_opa(volume_screen.containers[container_index].mutesolo_touchzone, LV_OPA_0, 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].mutesolo_touchzone, 15, 0);
      lv_obj_align(volume_screen.containers[container_index].mutesolo_touchzone, LV_ALIGN_BOTTOM_LEFT, 0, 0);
      lv_obj_add_event_cb(volume_screen.containers[container_index].mutesolo_touchzone, mutesolo_touchzone_click_handler, LV_EVENT_SHORT_CLICKED, (void *)(intptr_t)actual_index);
      lv_obj_add_event_cb(volume_screen.containers[container_index].mutesolo_touchzone, mutesolo_touchzone_click_handler, LV_EVENT_LONG_PRESSED, (void *)(intptr_t)actual_index);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].mutesolo_touchzone, 0, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].mutesolo_touchzone, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_flag(volume_screen.containers[container_index].mutesolo_touchzone, LV_OBJ_FLAG_EVENT_BUBBLE);

      // Création du solo_indicator à l'intérieur du mutesolo_touchzone
      volume_screen.containers[container_index].solo_indicator = lv_obj_create(volume_screen.containers[container_index].mutesolo_touchzone);
      lv_obj_set_size(volume_screen.containers[container_index].solo_indicator, 22, 22);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].solo_indicator, 4, 0);
      lv_obj_set_style_border_color(volume_screen.containers[container_index].solo_indicator, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].solo_indicator, 4, 0);
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].solo_indicator, COULEUR_GRIS, 0);
      lv_obj_align(volume_screen.containers[container_index].solo_indicator, LV_ALIGN_CENTER, 2, 15);
      lv_obj_clear_flag(volume_screen.containers[container_index].solo_indicator, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(volume_screen.containers[container_index].solo_indicator, &style_shadow_little, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].solo_indicator, LV_OBJ_FLAG_CLICKABLE);     
                                 
      
      
      // Création du mute_indicator à l'intérieur du mutesolo_touchzone
      volume_screen.containers[container_index].mute_indicator = lv_obj_create(volume_screen.containers[container_index].mutesolo_touchzone);
      lv_obj_set_size(volume_screen.containers[container_index].mute_indicator, 22, 22);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].mute_indicator, 4, 0);
      lv_obj_set_style_border_color(volume_screen.containers[container_index].mute_indicator, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].mute_indicator, 4, 0);
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].mute_indicator, COULEUR_ORANGE, 0);
      lv_obj_align(volume_screen.containers[container_index].mute_indicator, LV_ALIGN_CENTER, -10, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].mute_indicator, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(volume_screen.containers[container_index].mute_indicator, &style_shadow_little, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].mute_indicator, LV_OBJ_FLAG_CLICKABLE);
      







      volume_screen.containers[container_index].arm_touchzone = lv_obj_create(volume_screen.containers[container_index].container);
      lv_obj_set_size(volume_screen.containers[container_index].arm_touchzone, lv_pct(50), lv_pct(40));
      lv_obj_set_style_border_width(volume_screen.containers[container_index].arm_touchzone, 0, 0);
      lv_obj_set_style_bg_opa(volume_screen.containers[container_index].arm_touchzone, LV_OPA_0, 0);
      lv_obj_align(volume_screen.containers[container_index].arm_touchzone, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
      lv_obj_add_event_cb(volume_screen.containers[container_index].arm_touchzone, folder_touchzone_click_handler, LV_EVENT_SHORT_CLICKED, (void *)(intptr_t)actual_index);
      lv_obj_add_event_cb(volume_screen.containers[container_index].arm_touchzone, folder_touchzone_click_handler, LV_EVENT_LONG_PRESSED, (void *)(intptr_t)actual_index);
      lv_obj_clear_flag(volume_screen.containers[container_index].arm_touchzone, LV_OBJ_FLAG_SCROLLABLE);

      // Création du arm_indicator à l'intérieur du arm_touchzone
      volume_screen.containers[container_index].arm_indicator = lv_obj_create(volume_screen.containers[container_index].arm_touchzone);
      lv_obj_set_size(volume_screen.containers[container_index].arm_indicator, 22, 22);
      lv_obj_set_style_border_width(volume_screen.containers[container_index].arm_indicator, 4, 0);
      lv_obj_set_style_border_color(volume_screen.containers[container_index].arm_indicator, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_radius(volume_screen.containers[container_index].arm_indicator, 4, 0);
      lv_obj_set_style_bg_color(volume_screen.containers[container_index].arm_indicator, lv_color_make(0, 120, 0), 0);
      lv_obj_align(volume_screen.containers[container_index].arm_indicator, LV_ALIGN_CENTER, 0, 10);
      lv_obj_clear_flag(volume_screen.containers[container_index].arm_indicator, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_add_style(volume_screen.containers[container_index].arm_indicator, &style_shadow_little, 0);
      lv_obj_clear_flag(volume_screen.containers[container_index].arm_indicator, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_flag(volume_screen.containers[container_index].arm_touchzone, LV_OBJ_FLAG_EVENT_BUBBLE);

      container_index++;
}

lv_obj_t *create_scr_liv_vol(void)
{
      lv_obj_t *scr = lv_obj_create(NULL);
      lv_obj_set_style_bg_color(scr, COULEUR_GRIS, 0);

      // Initialiser les styles
      init_styles();

      lv_obj_t *left_band = lv_obj_create(scr);
      lv_obj_set_size(left_band, lv_pct(LEFTBANDWIDTHINPERCENT), lv_pct(100));
      lv_obj_align(left_band, LV_ALIGN_LEFT_MID, 0, 0);
      lv_obj_set_flex_flow(left_band, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(left_band, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
      lv_obj_add_style(left_band, &style_leftband, 0);

      // Ajouter les 5 labels dans left_band
      const char *labels[] = {"learn.", "track.", "", "device.", "browser."};
      for (int i = 0; i < 5; i++)
      {
            lv_obj_t *container = lv_obj_create(left_band);
            lv_obj_set_size(container, lv_pct(100), LV_SIZE_CONTENT);
            lv_obj_align(container, LV_ALIGN_LEFT_MID, 0, 0);
            lv_obj_set_style_bg_opa(container, LV_OPA_0, 0);
            lv_obj_set_style_border_width(container, 0, 0);
            lv_obj_set_style_pad_all(container, 0, 0);
            lv_obj_clear_flag(container, LV_OBJ_FLAG_SCROLLABLE);
            lv_obj_add_flag(container, LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(container, common_leftband_label_handler, LV_EVENT_SHORT_CLICKED, NULL);

            lv_obj_t *label = lv_label_create(container);
            lv_label_set_text(label, labels[i]);
            lv_obj_align(label, LV_ALIGN_LEFT_MID, 5, 0);
            lv_obj_set_style_text_font(label, POLICE_14, 0);
            lv_obj_add_style(label, &style_mode_others, 0);
            lv_obj_add_flag(label, LV_OBJ_FLAG_CLICKABLE);
            lv_obj_add_event_cb(label, common_leftband_label_handler, LV_EVENT_SHORT_CLICKED, NULL);

            // Si c'est "volume.", décaler le texte pour laisser de la place au cercle
            if (strcmp(labels[i], "volume.") == 0)
            {
                  lv_obj_align(label, LV_ALIGN_LEFT_MID, 30, 0);
            }

            lv_obj_center(container);
      }

      // Créer les trois bandes horizontales sans flex
      lv_obj_t *top_band = lv_obj_create(scr);
      lv_obj_set_size(top_band, lv_pct(100 - LEFTBANDWIDTHINPERCENT), lv_pct(40));
      lv_obj_align(top_band, LV_ALIGN_TOP_RIGHT, 0, 0);
      lv_obj_add_style(top_band, &style_fondslider, 0);

      lv_obj_t *middle_band = lv_obj_create(scr);
      lv_obj_set_size(middle_band, lv_pct(100 - LEFTBANDWIDTHINPERCENT), lv_pct(15));
      lv_obj_align(middle_band, LV_ALIGN_LEFT_MID, DECALAGEMODESELECTED, 0);
      lv_obj_set_style_bg_color(middle_band, COULEUR_GRIS_FONCE, 0);
      lv_obj_add_style(middle_band, &style_middleband, 0);
      lv_obj_set_style_radius(middle_band, 10, 0);
      lv_obj_clear_flag(middle_band, LV_OBJ_FLAG_SCROLLABLE);
      lv_obj_set_style_shadow_width(middle_band, 10, 0);
      lv_obj_set_style_shadow_color(middle_band, lv_color_black(), 0);
      lv_obj_set_style_shadow_opa(middle_band, LV_OPA_50, 0);
      lv_obj_set_style_shadow_ofs_x(middle_band, 5, 0);
      lv_obj_set_style_shadow_ofs_y(middle_band, 5, 0);
      lv_obj_set_style_shadow_spread(middle_band, 2, 0);

      lv_obj_t *bottom_band = lv_obj_create(scr);
      lv_obj_set_size(bottom_band, lv_pct(100 - LEFTBANDWIDTHINPERCENT), lv_pct(40));
      lv_obj_align(bottom_band, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
      lv_obj_add_style(bottom_band, &style_fondslider, 0);

      // Créer les 4 containers en haut avec positionnement absolu
      for (int i = 0; i < 4; i++)
      {
            create_container(top_band, "", "-inf dB", true);
            lv_obj_set_pos(volume_screen.containers[i].container, lv_pct(25 * i), 0);
      }

      // Créer les 4 containers en bas avec positionnement absolu
      for (int i = 0; i < 4; i++)
      {
            create_container(bottom_band, "", "-inf dB", false);
            lv_obj_set_pos(volume_screen.containers[i + 4].container, lv_pct(25 * i), 0);
      }

      // Créer le label vol.
      volume_screen.volume_mode_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_mode_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_mode_label, POLICE_14, 0);
      lv_label_set_text(volume_screen.volume_mode_label, "vol.");
      lv_obj_add_style(volume_screen.volume_mode_label, &style_container_label, 0);
      lv_obj_align(volume_screen.volume_mode_label, LV_ALIGN_LEFT_MID, DEFAUTPADDING * 2, 0);
      lv_obj_add_flag(volume_screen.volume_mode_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(volume_screen.volume_mode_label, volume_mode_label_click_handler, LV_EVENT_SHORT_CLICKED, (void *)0);

      // Créer le label pan.
      volume_screen.volume_pan_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_pan_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_pan_label, POLICE_14, 0);
      lv_obj_set_style_text_color(volume_screen.volume_pan_label, COULEUR_GRIS_CLAIR, 0);
      lv_label_set_text(volume_screen.volume_pan_label, "pan.");
      lv_obj_add_style(volume_screen.volume_pan_label, &style_container_label, 0);
      lv_obj_align_to(volume_screen.volume_pan_label, volume_screen.volume_mode_label, LV_ALIGN_LEFT_MID, 60, 0);
      lv_obj_add_flag(volume_screen.volume_pan_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(volume_screen.volume_pan_label, volume_mode_label_click_handler, LV_EVENT_SHORT_CLICKED, (void *)1);

      // Créer le label send.
      volume_screen.volume_send_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_send_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_send_label, POLICE_14, 0);
      lv_obj_set_style_text_color(volume_screen.volume_send_label, COULEUR_GRIS_CLAIR, 0);
      lv_label_set_text(volume_screen.volume_send_label, "send.");
      lv_obj_add_style(volume_screen.volume_send_label, &style_container_label, 0);
      lv_obj_align_to(volume_screen.volume_send_label, volume_screen.volume_pan_label, LV_ALIGN_LEFT_MID, 60, 0);
      lv_obj_add_flag(volume_screen.volume_send_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(volume_screen.volume_send_label, volume_mode_label_click_handler, LV_EVENT_SHORT_CLICKED, (void *)2);

      // Créer le label "sendName"
      volume_screen.volume_mode_sendname_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_mode_sendname_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_mode_sendname_label, POLICE_14, 0);
      lv_label_set_text(volume_screen.volume_mode_sendname_label, "Send Name");
      lv_obj_add_style(volume_screen.volume_mode_sendname_label, &style_container_label, 0);
      lv_obj_align_to(volume_screen.volume_mode_sendname_label, volume_screen.volume_send_label, LV_ALIGN_LEFT_MID, 80, 0);

      // Créer le label "page"
      volume_screen.volume_mode_page_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_mode_page_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_mode_page_label, POLICE_14, 0);
      lv_label_set_text(volume_screen.volume_mode_page_label, "Page 1/1");
      lv_obj_add_style(volume_screen.volume_mode_page_label, &style_container_label, 0);
      lv_obj_align(volume_screen.volume_mode_page_label, LV_ALIGN_RIGHT_MID, -DEFAUTPADDING, 0);

      // Créer le label "add track"
      volume_screen.volume_mode_add_track_label = lv_label_create(middle_band);
      lv_obj_set_size(volume_screen.volume_mode_add_track_label, LV_SIZE_CONTENT, LV_SIZE_CONTENT);
      lv_obj_set_style_text_font(volume_screen.volume_mode_add_track_label, POLICE_14, 0);
      lv_obj_set_style_text_color(volume_screen.volume_mode_add_track_label, COULEUR_BEIGE, 0);
      lv_label_set_text(volume_screen.volume_mode_add_track_label, "+");
      lv_obj_align_to(volume_screen.volume_mode_add_track_label, volume_screen.volume_mode_page_label, LV_ALIGN_LEFT_MID, -50, 0);
      lv_obj_add_flag(volume_screen.volume_mode_add_track_label, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(volume_screen.volume_mode_add_track_label, volume_mode_add_track_label_click_handler, LV_EVENT_SHORT_CLICKED, (void *)3);

      // Créer les masques après la création des containers
      for (int i = 0; i < 8; i++)
      {
            if (volume_screen.containers[i].mask != NULL)
            {
                  lv_obj_clear_flag(volume_screen.containers[i].mask, LV_OBJ_FLAG_HIDDEN);
            }
      }

      
      ///////////////////////////////////////////////////
      ////////////PopUpMenu
      ///////////////////////////////////////////////////

      volume_screen.PopUpMenu.popup_menu_background = lv_obj_create(scr);
      lv_obj_add_flag(volume_screen.PopUpMenu.popup_menu_background, LV_OBJ_FLAG_HIDDEN);
      lv_obj_set_size(volume_screen.PopUpMenu.popup_menu_background, lv_pct(100), lv_pct(100));
      lv_obj_align(volume_screen.PopUpMenu.popup_menu_background, LV_ALIGN_LEFT_MID, 0, 0);
      lv_obj_set_style_bg_color(volume_screen.PopUpMenu.popup_menu_background, lv_color_hex(0x000000), 0);
      lv_obj_set_style_radius(volume_screen.PopUpMenu.popup_menu_background, 0, 0);
      lv_obj_set_style_border_width(volume_screen.PopUpMenu.popup_menu_background, 0, 0);
      lv_obj_set_style_bg_opa(volume_screen.PopUpMenu.popup_menu_background, LV_OPA_90, 0);
      lv_obj_add_flag(volume_screen.PopUpMenu.popup_menu_background, LV_OBJ_FLAG_CLICKABLE);
      lv_obj_add_event_cb(volume_screen.PopUpMenu.popup_menu_background, volume_mode_popup_menu_close_handler, LV_EVENT_SHORT_CLICKED, NULL);

      // Création du container flex pour organiser verticalement les options
      lv_obj_t *options_container = lv_obj_create(volume_screen.PopUpMenu.popup_menu_background);
      lv_obj_set_size(options_container, lv_pct(40), lv_pct(78));
      lv_obj_center(options_container);
      lv_obj_set_style_bg_color(options_container, COULEUR_GRIS_FONCE, 0);
      lv_obj_set_style_radius(options_container, 0, 0);
      lv_obj_set_style_border_width(options_container, 0, 0);
      lv_obj_set_flex_flow(options_container, LV_FLEX_FLOW_COLUMN);
      lv_obj_set_flex_align(options_container, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
      lv_obj_set_style_pad_row(options_container, 20, 0);
      lv_obj_set_style_bg_opa(options_container, LV_OPA_100, 0);
      lv_obj_set_style_border_width(options_container, 0, 0);

      // Création des options du menu popup
      const struct {
          const char *label_text;
          void *user_data;
      } menu_options[] = {
          {"Add MIDI Track", (void *)0},
          {"Add Audio Track", (void *)1},
          {"Add Return Track", (void *)2},
          {"Delete Current Track", (void *)3}
      };

      for (int i = 0; i < sizeof(menu_options) / sizeof(menu_options[0]); i++) {
          // Création du container
          lv_obj_t *option_button = lv_btn_create(options_container);
          lv_obj_set_size(option_button, lv_pct(100), lv_pct(20));
          lv_obj_set_style_pad_all(option_button, 20, 0);
          lv_obj_set_style_bg_color(option_button, COULEUR_GRIS_MOYEN, 0);
          lv_obj_set_style_bg_opa(option_button, LV_OPA_100, 0);
          lv_obj_set_style_border_width(option_button, 0, 0);
          lv_obj_clear_flag(option_button, LV_OBJ_FLAG_SCROLLABLE);
          
          // Suppression de l'effet 3D          
          lv_obj_set_style_bg_grad_dir(option_button, LV_GRAD_DIR_NONE, 0);
          lv_obj_set_style_bg_grad_color(option_button, COULEUR_GRIS_MOYEN, 0);
          lv_obj_set_style_bg_grad_stop(option_button, 255, 0);
          lv_obj_add_style(option_button, &style_shadow, 0);
          
          // Ajout de l'effet de pression
          lv_obj_set_style_transform_width(option_button, -5, LV_STATE_PRESSED);
          lv_obj_set_style_transform_height(option_button, -5, LV_STATE_PRESSED);
          
          lv_obj_add_event_cb(option_button, volume_mode_choose_track_to_add_handler, LV_EVENT_RELEASED, menu_options[i].user_data);

          // Création du label
          lv_obj_t *option_label = lv_label_create(option_button);
          lv_obj_set_style_text_font(option_label, POLICE_14, 0);
          lv_obj_set_style_text_color(option_label, COULEUR_BEIGE, 0);
          lv_label_set_text(option_label, menu_options[i].label_text);
          lv_obj_center(option_label);

          // Stockage des références dans la structure volume_screen
          switch (i) {
              case 0:
                  volume_screen.PopUpMenu.popup_menu_add_midi_track_container = option_button;
                  volume_screen.PopUpMenu.popup_menu_add_midi_track_label = option_label;
                  break;
              case 1:
                  volume_screen.PopUpMenu.popup_menu_add_audio_track_container = option_button;
                  volume_screen.PopUpMenu.popup_menu_add_audio_track_label = option_label;
                  break;
              case 2:
                  volume_screen.PopUpMenu.popup_menu_add_return_track_container = option_button;
                  volume_screen.PopUpMenu.popup_menu_add_return_track_label = option_label;
                  break;
              case 3:
                  volume_screen.PopUpMenu.popup_menu_delete_track_container = option_button;
                  volume_screen.PopUpMenu.popup_menu_delete_track_label = option_label;
                  // Ajout d'une couleur différente pour l'option de suppression
                  lv_obj_set_style_bg_color(option_button, lv_color_make(180, 0, 0), 0);
                  break;
          }
      }

      return scr;
}